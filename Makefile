# VeloMasters Makefile

.PHONY: help build run dev test clean migrate-up migrate-down sqlc-generate

# Переменные
BINARY_NAME=velomasters
MAIN_PATH=./cmd/server
MIGRATIONS_PATH=./migrations

# Помощь
help:
	@echo "Доступные команды:"
	@echo "  build         - Собрать приложение"
	@echo "  run           - Запустить приложение"
	@echo "  dev           - Запустить в режиме разработки"
	@echo "  test          - Запустить тесты"
	@echo "  clean         - Очистить собранные файлы"
	@echo "  migrate-up    - Применить миграции"
	@echo "  migrate-down  - Откатить миграции"
	@echo "  sqlc-generate - Сгенерировать код из SQL"

# Сборка приложения
build:
	@echo "Сборка приложения..."
	go build -o bin/$(BINARY_NAME) $(MAIN_PATH)

# Запуск приложения
run: build
	@echo "Запуск приложения..."
	./bin/$(BINARY_NAME)

# Режим разработки
dev:
	@echo "Запуск в режиме разработки..."
	go run $(MAIN_PATH)

# Тесты
test:
	@echo "Запуск тестов..."
	go test -v ./...

# Очистка
clean:
	@echo "Очистка..."
	rm -rf bin/
	go clean

# Применение миграций
migrate-up:
	@echo "Применение миграций..."
	migrate -path $(MIGRATIONS_PATH) -database "postgres://postgres:your_password_here@localhost:5432/velomasters?sslmode=disable" up

# Откат миграций
migrate-down:
	@echo "Откат миграций..."
	migrate -path $(MIGRATIONS_PATH) -database "postgres://postgres:your_password_here@localhost:5432/velomasters?sslmode=disable" down

# Генерация кода SQLC
sqlc-generate:
	@echo "Генерация кода из SQL..."
	./sqlc generate

# Установка зависимостей
deps:
	@echo "Установка зависимостей..."
	go mod download
	go mod tidy

# Создание директорий
dirs:
	@echo "Создание директорий..."
	mkdir -p bin/
	mkdir -p web/static/css/
	mkdir -p web/static/js/

# Сборка CSS
css-build:
	@echo "Сборка CSS..."
	./tailwindcss.exe -i ./web/static/css/input.css -o ./web/static/css/output.css

css-watch:
	@echo "Отслеживание изменений CSS..."
	./tailwindcss.exe -i ./web/static/css/input.css -o ./web/static/css/output.css --watch

css-prod:
	@echo "Сборка CSS для продакшена..."
	./tailwindcss.exe -i ./web/static/css/input.css -o ./web/static/css/output.css --minify

# Генерация Templ шаблонов
templ-generate:
	@echo "Генерация Templ шаблонов..."
	templ generate

# Полная сборка
build-all: css-build templ-generate sqlc-generate build

.PHONY: build run dev test clean migrate-up migrate-down sqlc-generate css-build css-watch css-prod templ-generate build-all dirs deps
