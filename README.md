# VeloMasters - Система учета для вело-мастерской

Система управления задачами, услугами и каталогом запчастей для велосипедной мастерской.

## Технологический стек

- **Backend**: Go + Fiber
- **Frontend**: Templ + HTMX + TailwindCSS
- **База данных**: Postgres

## Структура проекта

```
velomasters/
├── cmd/
│   └── server/
│       └── main.go          # Точка входа приложения
├── internal/
│   ├── config/              # Конфигурация
│   ├── database/            # Работа с БД
│   ├── handlers/            # HTTP обработчики
│   ├── models/              # Модели данных
│   ├── services/            # Бизнес-логика
│   └── templates/           # Templ шаблоны
├── web/
│   ├── static/              # Статические файлы
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── dist/                # Собранные файлы
├── migrations/              # Миграции БД
├── go.mod
├── go.sum
├── tailwind.config.js
├── package.json
└── README.md
```

## Функциональность

### Модуль задач
- Создание и управление задачами ремонта
- Отслеживание статуса выполнения
- Привязка к клиентам и велосипедам

### Модуль услуг
- Каталог услуг мастерской
- Управление ценами
- Описания и категории услуг

### Модуль запчастей
- Каталог запчастей
- Учет остатков на складе
- Управление поставщиками и ценами

## Запуск проекта

```bash
# Установка зависимостей
go mod tidy
npm install

# Сборка CSS
npm run build-css

# Генерация Templ шаблонов
templ generate

# Запуск сервера
go run cmd/server/main.go
```
