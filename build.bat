@echo off
chcp 65001 >nul
echo ========================================
echo VeloMasters - Build Project
echo ========================================

echo.
echo [1/4] Generating Templ templates...
templ generate
if %errorlevel% neq 0 (
    echo Error generating Templ templates
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo Templ templates generated successfully!

echo.
echo [2/4] Building CSS...
.\tailwindcss.exe -i .\web\static\css\input.css -o .\web\static\css\output.css --minify
if %errorlevel% neq 0 (
    echo Error building CSS
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo CSS built successfully!

echo.
echo [3/4] Generating SQLC code...
.\sqlc.exe generate
if %errorlevel% neq 0 (
    echo Error generating SQLC code
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SQLC code generated successfully!

echo.
echo [4/4] Building Go application...
go build -o bin\velomasters.exe .\cmd\server
if %errorlevel% neq 0 (
    echo Error building Go application
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo Go application built successfully!

echo.
echo ========================================
echo Build completed successfully!
echo Executable: bin\velomasters.exe
echo ========================================
echo Press any key to exit...
pause >nul
