package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"velomasters/internal/config"
	"velomasters/internal/database"
	"velomasters/internal/handlers"
	"velomasters/internal/services"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/joho/godotenv"

	// Импорт драйвера PostgreSQL для миграций
	_ "github.com/lib/pq"
)

func main() {
	// Загружаем .env файл (игнорируем ошибку, если файл не найден)
	_ = godotenv.Load()

	// Загружаем конфигурацию
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Подключаемся к базе данных
	db, err := database.New(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Запускаем миграции
	if err := database.RunMigrations(cfg.Database.DatabaseURL()); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Создаем сервисы
	services := services.NewServices(db.Pool)

	// Создаем обработчики
	handlers := handlers.NewHandlers(services)

	// Создаем Fiber приложение
	app := fiber.New(fiber.Config{
		AppName:      "VeloMasters v1.0",
		ServerHeader: "VeloMasters",
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}

			log.Printf("Error: %v", err)

			return c.Status(code).JSON(fiber.Map{
				"error": err.Error(),
			})
		},
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization,X-Requested-With",
	}))

	// Статические файлы
	app.Static("/static", "./web/static")

	// Настраиваем маршруты
	handlers.SetupRoutes(app)

	// Graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Gracefully shutting down...")
		_ = app.Shutdown()
	}()

	// Запускаем сервер
	log.Printf("Starting server on %s:%d", cfg.Server.Host, cfg.Server.Port)
	if err := app.Listen(cfg.Server.Address()); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
