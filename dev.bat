@echo off
chcp 65001 >nul
echo ========================================
echo VeloMasters - Development Mode
echo ========================================

echo.
echo [1/3] Generating Templ templates...
templ generate
if %errorlevel% neq 0 (
    echo Error generating Templ templates
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo Templ templates generated successfully!

echo.
echo [2/3] Building CSS...
.\tailwindcss.exe -i .\web\static\css\input.css -o .\web\static\css\output.css
if %errorlevel% neq 0 (
    echo Error building CSS
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo CSS built successfully!

echo.
echo [3/3] Starting development server...
echo Server will be available at: http://localhost:8080
echo Press Ctrl+C to stop the server
echo.

go run .\cmd\server
