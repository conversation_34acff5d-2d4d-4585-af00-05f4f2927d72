-- name: CreateBicycle :one
INSERT INTO bicycles (client_id, brand, model, year, color, frame_number, description)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING *;

-- name: GetBicycle :one
SELECT * FROM bicycles
WHERE id = $1;

-- name: GetBicyclesByClient :many
SELECT * FROM bicycles
WHERE client_id = $1
ORDER BY brand, model;

-- name: ListBicycles :many
SELECT b.*, c.name as client_name
FROM bicycles b
JOIN clients c ON b.client_id = c.id
ORDER BY c.name, b.brand, b.model;

-- name: SearchBicycles :many
SELECT b.*, c.name as client_name
FROM bicycles b
JOIN clients c ON b.client_id = c.id
WHERE b.brand ILIKE '%' || $1 || '%'
   OR b.model ILIKE '%' || $1 || '%'
   OR b.frame_number ILIKE '%' || $1 || '%'
   OR c.name ILIKE '%' || $1 || '%'
ORDER BY c.name, b.brand, b.model;

-- name: UpdateBicycle :one
UPDATE bicycles
SET brand = $2, model = $3, year = $4, color = $5, frame_number = $6, description = $7, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteBicycle :exec
DELETE FROM bicycles
WHERE id = $1;
