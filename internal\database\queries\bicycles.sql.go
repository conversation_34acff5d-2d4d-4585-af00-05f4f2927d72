// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: bicycles.sql

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createBicycle = `-- name: CreateBicycle :one
INSERT INTO bicycles (client_id, brand, model, year, color, frame_number, description)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING id, client_id, brand, model, year, color, frame_number, description, created_at, updated_at
`

type CreateBicycleParams struct {
	ClientID    pgtype.UUID `json:"client_id"`
	Brand       pgtype.Text `json:"brand"`
	Model       pgtype.Text `json:"model"`
	Year        pgtype.Int4 `json:"year"`
	Color       pgtype.Text `json:"color"`
	FrameNumber pgtype.Text `json:"frame_number"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) CreateBicycle(ctx context.Context, arg CreateBicycleParams) (Bicycle, error) {
	row := q.db.QueryRow(ctx, createBicycle,
		arg.ClientID,
		arg.Brand,
		arg.Model,
		arg.Year,
		arg.Color,
		arg.FrameNumber,
		arg.Description,
	)
	var i Bicycle
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.Brand,
		&i.Model,
		&i.Year,
		&i.Color,
		&i.FrameNumber,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteBicycle = `-- name: DeleteBicycle :exec
DELETE FROM bicycles
WHERE id = $1
`

func (q *Queries) DeleteBicycle(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteBicycle, id)
	return err
}

const getBicycle = `-- name: GetBicycle :one
SELECT id, client_id, brand, model, year, color, frame_number, description, created_at, updated_at FROM bicycles
WHERE id = $1
`

func (q *Queries) GetBicycle(ctx context.Context, id pgtype.UUID) (Bicycle, error) {
	row := q.db.QueryRow(ctx, getBicycle, id)
	var i Bicycle
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.Brand,
		&i.Model,
		&i.Year,
		&i.Color,
		&i.FrameNumber,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getBicyclesByClient = `-- name: GetBicyclesByClient :many
SELECT id, client_id, brand, model, year, color, frame_number, description, created_at, updated_at FROM bicycles
WHERE client_id = $1
ORDER BY brand, model
`

func (q *Queries) GetBicyclesByClient(ctx context.Context, clientID pgtype.UUID) ([]Bicycle, error) {
	rows, err := q.db.Query(ctx, getBicyclesByClient, clientID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Bicycle{}
	for rows.Next() {
		var i Bicycle
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.Brand,
			&i.Model,
			&i.Year,
			&i.Color,
			&i.FrameNumber,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listBicycles = `-- name: ListBicycles :many
SELECT b.id, b.client_id, b.brand, b.model, b.year, b.color, b.frame_number, b.description, b.created_at, b.updated_at, c.name as client_name
FROM bicycles b
JOIN clients c ON b.client_id = c.id
ORDER BY c.name, b.brand, b.model
`

type ListBicyclesRow struct {
	ID          pgtype.UUID        `json:"id"`
	ClientID    pgtype.UUID        `json:"client_id"`
	Brand       pgtype.Text        `json:"brand"`
	Model       pgtype.Text        `json:"model"`
	Year        pgtype.Int4        `json:"year"`
	Color       pgtype.Text        `json:"color"`
	FrameNumber pgtype.Text        `json:"frame_number"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
	ClientName  string             `json:"client_name"`
}

func (q *Queries) ListBicycles(ctx context.Context) ([]ListBicyclesRow, error) {
	rows, err := q.db.Query(ctx, listBicycles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListBicyclesRow{}
	for rows.Next() {
		var i ListBicyclesRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.Brand,
			&i.Model,
			&i.Year,
			&i.Color,
			&i.FrameNumber,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchBicycles = `-- name: SearchBicycles :many
SELECT b.id, b.client_id, b.brand, b.model, b.year, b.color, b.frame_number, b.description, b.created_at, b.updated_at, c.name as client_name
FROM bicycles b
JOIN clients c ON b.client_id = c.id
WHERE b.brand ILIKE '%' || $1 || '%'
   OR b.model ILIKE '%' || $1 || '%'
   OR b.frame_number ILIKE '%' || $1 || '%'
   OR c.name ILIKE '%' || $1 || '%'
ORDER BY c.name, b.brand, b.model
`

type SearchBicyclesRow struct {
	ID          pgtype.UUID        `json:"id"`
	ClientID    pgtype.UUID        `json:"client_id"`
	Brand       pgtype.Text        `json:"brand"`
	Model       pgtype.Text        `json:"model"`
	Year        pgtype.Int4        `json:"year"`
	Color       pgtype.Text        `json:"color"`
	FrameNumber pgtype.Text        `json:"frame_number"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
	ClientName  string             `json:"client_name"`
}

func (q *Queries) SearchBicycles(ctx context.Context, dollar_1 pgtype.Text) ([]SearchBicyclesRow, error) {
	rows, err := q.db.Query(ctx, searchBicycles, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []SearchBicyclesRow{}
	for rows.Next() {
		var i SearchBicyclesRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.Brand,
			&i.Model,
			&i.Year,
			&i.Color,
			&i.FrameNumber,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateBicycle = `-- name: UpdateBicycle :one
UPDATE bicycles
SET brand = $2, model = $3, year = $4, color = $5, frame_number = $6, description = $7, updated_at = NOW()
WHERE id = $1
RETURNING id, client_id, brand, model, year, color, frame_number, description, created_at, updated_at
`

type UpdateBicycleParams struct {
	ID          pgtype.UUID `json:"id"`
	Brand       pgtype.Text `json:"brand"`
	Model       pgtype.Text `json:"model"`
	Year        pgtype.Int4 `json:"year"`
	Color       pgtype.Text `json:"color"`
	FrameNumber pgtype.Text `json:"frame_number"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) UpdateBicycle(ctx context.Context, arg UpdateBicycleParams) (Bicycle, error) {
	row := q.db.QueryRow(ctx, updateBicycle,
		arg.ID,
		arg.Brand,
		arg.Model,
		arg.Year,
		arg.Color,
		arg.FrameNumber,
		arg.Description,
	)
	var i Bicycle
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.Brand,
		&i.Model,
		&i.Year,
		&i.Color,
		&i.FrameNumber,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
