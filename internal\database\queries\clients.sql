-- name: CreateClient :one
INSERT INTO clients (name, phone, email, address, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: GetClient :one
SELECT * FROM clients
WHERE id = $1;

-- name: GetClientByPhone :one
SELECT * FROM clients
WHERE phone = $1;

-- name: ListClients :many
SELECT * FROM clients
ORDER BY name;

-- name: SearchClients :many
SELECT * FROM clients
WHERE name ILIKE '%' || $1 || '%' 
   OR phone ILIKE '%' || $1 || '%'
   OR email ILIKE '%' || $1 || '%'
ORDER BY name;

-- name: UpdateClient :one
UPDATE clients
SET name = $2, phone = $3, email = $4, address = $5, notes = $6, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteClient :exec
DELETE FROM clients
WHERE id = $1;
