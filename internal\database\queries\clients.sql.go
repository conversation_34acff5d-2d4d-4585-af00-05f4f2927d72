// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: clients.sql

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createClient = `-- name: CreateClient :one
INSERT INTO clients (name, phone, email, address, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, name, phone, email, address, notes, created_at, updated_at
`

type CreateClientParams struct {
	Name    string      `json:"name"`
	Phone   pgtype.Text `json:"phone"`
	Email   pgtype.Text `json:"email"`
	Address pgtype.Text `json:"address"`
	Notes   pgtype.Text `json:"notes"`
}

func (q *Queries) CreateClient(ctx context.Context, arg CreateClientParams) (Client, error) {
	row := q.db.QueryRow(ctx, createClient,
		arg.Name,
		arg.Phone,
		arg.Email,
		arg.Address,
		arg.Notes,
	)
	var i Client
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteClient = `-- name: DeleteClient :exec
DELETE FROM clients
WHERE id = $1
`

func (q *Queries) DeleteClient(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteClient, id)
	return err
}

const getClient = `-- name: GetClient :one
SELECT id, name, phone, email, address, notes, created_at, updated_at FROM clients
WHERE id = $1
`

func (q *Queries) GetClient(ctx context.Context, id pgtype.UUID) (Client, error) {
	row := q.db.QueryRow(ctx, getClient, id)
	var i Client
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getClientByPhone = `-- name: GetClientByPhone :one
SELECT id, name, phone, email, address, notes, created_at, updated_at FROM clients
WHERE phone = $1
`

func (q *Queries) GetClientByPhone(ctx context.Context, phone pgtype.Text) (Client, error) {
	row := q.db.QueryRow(ctx, getClientByPhone, phone)
	var i Client
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const listClients = `-- name: ListClients :many
SELECT id, name, phone, email, address, notes, created_at, updated_at FROM clients
ORDER BY name
`

func (q *Queries) ListClients(ctx context.Context) ([]Client, error) {
	rows, err := q.db.Query(ctx, listClients)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Client{}
	for rows.Next() {
		var i Client
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Phone,
			&i.Email,
			&i.Address,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchClients = `-- name: SearchClients :many
SELECT id, name, phone, email, address, notes, created_at, updated_at FROM clients
WHERE name ILIKE '%' || $1 || '%' 
   OR phone ILIKE '%' || $1 || '%'
   OR email ILIKE '%' || $1 || '%'
ORDER BY name
`

func (q *Queries) SearchClients(ctx context.Context, dollar_1 pgtype.Text) ([]Client, error) {
	rows, err := q.db.Query(ctx, searchClients, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Client{}
	for rows.Next() {
		var i Client
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Phone,
			&i.Email,
			&i.Address,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateClient = `-- name: UpdateClient :one
UPDATE clients
SET name = $2, phone = $3, email = $4, address = $5, notes = $6, updated_at = NOW()
WHERE id = $1
RETURNING id, name, phone, email, address, notes, created_at, updated_at
`

type UpdateClientParams struct {
	ID      pgtype.UUID `json:"id"`
	Name    string      `json:"name"`
	Phone   pgtype.Text `json:"phone"`
	Email   pgtype.Text `json:"email"`
	Address pgtype.Text `json:"address"`
	Notes   pgtype.Text `json:"notes"`
}

func (q *Queries) UpdateClient(ctx context.Context, arg UpdateClientParams) (Client, error) {
	row := q.db.QueryRow(ctx, updateClient,
		arg.ID,
		arg.Name,
		arg.Phone,
		arg.Email,
		arg.Address,
		arg.Notes,
	)
	var i Client
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
