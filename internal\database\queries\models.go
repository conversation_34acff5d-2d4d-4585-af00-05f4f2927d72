// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package queries

import (
	"database/sql/driver"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
)

type TaskPriority string

const (
	TaskPriorityLow    TaskPriority = "low"
	TaskPriorityNormal TaskPriority = "normal"
	TaskPriorityHigh   TaskPriority = "high"
	TaskPriorityUrgent TaskPriority = "urgent"
)

func (e *TaskPriority) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TaskPriority(s)
	case string:
		*e = TaskPriority(s)
	default:
		return fmt.Errorf("unsupported scan type for TaskPriority: %T", src)
	}
	return nil
}

type NullTaskPriority struct {
	TaskPriority TaskPriority `json:"task_priority"`
	Valid        bool         `json:"valid"` // Valid is true if TaskPriority is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullTaskPriority) Scan(value interface{}) error {
	if value == nil {
		ns.TaskPriority, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TaskPriority.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTaskPriority) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TaskPriority), nil
}

type TaskStatus string

const (
	TaskStatusPending       TaskStatus = "pending"
	TaskStatusInProgress    TaskStatus = "in_progress"
	TaskStatusWaitingParts  TaskStatus = "waiting_parts"
	TaskStatusWaitingClient TaskStatus = "waiting_client"
	TaskStatusCompleted     TaskStatus = "completed"
	TaskStatusCancelled     TaskStatus = "cancelled"
)

func (e *TaskStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TaskStatus(s)
	case string:
		*e = TaskStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for TaskStatus: %T", src)
	}
	return nil
}

type NullTaskStatus struct {
	TaskStatus TaskStatus `json:"task_status"`
	Valid      bool       `json:"valid"` // Valid is true if TaskStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTaskStatus) Scan(value interface{}) error {
	if value == nil {
		ns.TaskStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TaskStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTaskStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TaskStatus), nil
}

type Bicycle struct {
	ID          pgtype.UUID        `json:"id"`
	ClientID    pgtype.UUID        `json:"client_id"`
	Brand       pgtype.Text        `json:"brand"`
	Model       pgtype.Text        `json:"model"`
	Year        pgtype.Int4        `json:"year"`
	Color       pgtype.Text        `json:"color"`
	FrameNumber pgtype.Text        `json:"frame_number"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type Client struct {
	ID        pgtype.UUID        `json:"id"`
	Name      string             `json:"name"`
	Phone     pgtype.Text        `json:"phone"`
	Email     pgtype.Text        `json:"email"`
	Address   pgtype.Text        `json:"address"`
	Notes     pgtype.Text        `json:"notes"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

type Part struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
}

type PartCategory struct {
	ID          pgtype.UUID        `json:"id"`
	Name        string             `json:"name"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
}

type Service struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	Price           pgtype.Numeric     `json:"price"`
	DurationMinutes pgtype.Int4        `json:"duration_minutes"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
}

type ServiceCategory struct {
	ID          pgtype.UUID        `json:"id"`
	Name        string             `json:"name"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
}

type Supplier struct {
	ID            pgtype.UUID        `json:"id"`
	Name          string             `json:"name"`
	ContactPerson pgtype.Text        `json:"contact_person"`
	Phone         pgtype.Text        `json:"phone"`
	Email         pgtype.Text        `json:"email"`
	Address       pgtype.Text        `json:"address"`
	Website       pgtype.Text        `json:"website"`
	Notes         pgtype.Text        `json:"notes"`
	CreatedAt     pgtype.Timestamptz `json:"created_at"`
	UpdatedAt     pgtype.Timestamptz `json:"updated_at"`
}

type Task struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
}

type TaskPart struct {
	ID        pgtype.UUID        `json:"id"`
	TaskID    pgtype.UUID        `json:"task_id"`
	PartID    pgtype.UUID        `json:"part_id"`
	Quantity  int32              `json:"quantity"`
	Price     pgtype.Numeric     `json:"price"`
	Notes     pgtype.Text        `json:"notes"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
}

type TaskService struct {
	ID        pgtype.UUID        `json:"id"`
	TaskID    pgtype.UUID        `json:"task_id"`
	ServiceID pgtype.UUID        `json:"service_id"`
	Quantity  pgtype.Int4        `json:"quantity"`
	Price     pgtype.Numeric     `json:"price"`
	Notes     pgtype.Text        `json:"notes"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
}
