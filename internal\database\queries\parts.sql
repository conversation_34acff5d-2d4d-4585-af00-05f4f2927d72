-- name: CreatePartCategory :one
INSERT INTO part_categories (name, description)
VALUES ($1, $2)
RETURNING *;

-- name: GetPartCategory :one
SELECT * FROM part_categories
WHERE id = $1;

-- name: ListPartCategories :many
SELECT * FROM part_categories
ORDER BY name;

-- name: UpdatePartCategory :one
UPDATE part_categories
SET name = $2, description = $3
WHERE id = $1
RETURNING *;

-- name: DeletePartCategory :exec
DELETE FROM part_categories
WHERE id = $1;

-- name: CreateSupplier :one
INSERT INTO suppliers (name, contact_person, phone, email, address, website, notes)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING *;

-- name: GetSupplier :one
SELECT * FROM suppliers
WHERE id = $1;

-- name: ListSuppliers :many
SELECT * FROM suppliers
ORDER BY name;

-- name: SearchSuppliers :many
SELECT * FROM suppliers
WHERE name ILIKE '%' || $1 || '%'
   OR contact_person ILIKE '%' || $1 || '%'
   OR email ILIKE '%' || $1 || '%'
ORDER BY name;

-- name: UpdateSupplier :one
UPDATE suppliers
SET name = $2, contact_person = $3, phone = $4, email = $5, 
    address = $6, website = $7, notes = $8, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteSupplier :exec
DELETE FROM suppliers
WHERE id = $1;

-- name: CreatePart :one
INSERT INTO parts (category_id, supplier_id, name, description, part_number, brand, price, cost, quantity_in_stock, min_stock_level, is_active)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
RETURNING *;

-- name: GetPart :one
SELECT p.*, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.id = $1;

-- name: ListParts :many
SELECT p.*, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true
ORDER BY pc.name, p.name;

-- name: ListPartsByCategory :many
SELECT p.*, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.category_id = $1 AND p.is_active = true
ORDER BY p.name;

-- name: ListLowStockParts :many
SELECT p.*, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true AND p.quantity_in_stock <= p.min_stock_level
ORDER BY p.quantity_in_stock ASC;

-- name: SearchParts :many
SELECT p.*, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true
  AND (p.name ILIKE '%' || $1 || '%' 
       OR p.description ILIKE '%' || $1 || '%'
       OR p.part_number ILIKE '%' || $1 || '%'
       OR p.brand ILIKE '%' || $1 || '%')
ORDER BY p.name;

-- name: UpdatePart :one
UPDATE parts
SET category_id = $2, supplier_id = $3, name = $4, description = $5, 
    part_number = $6, brand = $7, price = $8, cost = $9, 
    quantity_in_stock = $10, min_stock_level = $11, is_active = $12, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdatePartStock :one
UPDATE parts
SET quantity_in_stock = $2, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeletePart :exec
DELETE FROM parts
WHERE id = $1;

-- name: AddPartToTask :one
INSERT INTO task_parts (task_id, part_id, quantity, price, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: GetTaskParts :many
SELECT tp.*, p.name as part_name, p.description as part_description, p.part_number
FROM task_parts tp
JOIN parts p ON tp.part_id = p.id
WHERE tp.task_id = $1
ORDER BY tp.created_at;

-- name: UpdateTaskPart :one
UPDATE task_parts
SET quantity = $3, price = $4, notes = $5
WHERE task_id = $1 AND part_id = $2
RETURNING *;

-- name: RemovePartFromTask :exec
DELETE FROM task_parts
WHERE task_id = $1 AND part_id = $2;
