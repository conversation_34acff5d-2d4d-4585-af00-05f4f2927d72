// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: parts.sql

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const addPartToTask = `-- name: AddPartToTask :one
INSERT INTO task_parts (task_id, part_id, quantity, price, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, task_id, part_id, quantity, price, notes, created_at
`

type AddPartToTaskParams struct {
	TaskID   pgtype.UUID    `json:"task_id"`
	PartID   pgtype.UUID    `json:"part_id"`
	Quantity int32          `json:"quantity"`
	Price    pgtype.Numeric `json:"price"`
	Notes    pgtype.Text    `json:"notes"`
}

func (q *Queries) AddPartToTask(ctx context.Context, arg AddPartToTaskParams) (TaskPart, error) {
	row := q.db.QueryRow(ctx, addPartToTask,
		arg.TaskID,
		arg.PartID,
		arg.Quantity,
		arg.Price,
		arg.Notes,
	)
	var i TaskPart
	err := row.Scan(
		&i.ID,
		&i.TaskID,
		&i.PartID,
		&i.Quantity,
		&i.Price,
		&i.Notes,
		&i.CreatedAt,
	)
	return i, err
}

const createPart = `-- name: CreatePart :one
INSERT INTO parts (category_id, supplier_id, name, description, part_number, brand, price, cost, quantity_in_stock, min_stock_level, is_active)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
RETURNING id, category_id, supplier_id, name, description, part_number, brand, price, cost, quantity_in_stock, min_stock_level, is_active, created_at, updated_at
`

type CreatePartParams struct {
	CategoryID      pgtype.UUID    `json:"category_id"`
	SupplierID      pgtype.UUID    `json:"supplier_id"`
	Name            string         `json:"name"`
	Description     pgtype.Text    `json:"description"`
	PartNumber      pgtype.Text    `json:"part_number"`
	Brand           pgtype.Text    `json:"brand"`
	Price           pgtype.Numeric `json:"price"`
	Cost            pgtype.Numeric `json:"cost"`
	QuantityInStock pgtype.Int4    `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4    `json:"min_stock_level"`
	IsActive        pgtype.Bool    `json:"is_active"`
}

func (q *Queries) CreatePart(ctx context.Context, arg CreatePartParams) (Part, error) {
	row := q.db.QueryRow(ctx, createPart,
		arg.CategoryID,
		arg.SupplierID,
		arg.Name,
		arg.Description,
		arg.PartNumber,
		arg.Brand,
		arg.Price,
		arg.Cost,
		arg.QuantityInStock,
		arg.MinStockLevel,
		arg.IsActive,
	)
	var i Part
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.SupplierID,
		&i.Name,
		&i.Description,
		&i.PartNumber,
		&i.Brand,
		&i.Price,
		&i.Cost,
		&i.QuantityInStock,
		&i.MinStockLevel,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createPartCategory = `-- name: CreatePartCategory :one
INSERT INTO part_categories (name, description)
VALUES ($1, $2)
RETURNING id, name, description, created_at
`

type CreatePartCategoryParams struct {
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) CreatePartCategory(ctx context.Context, arg CreatePartCategoryParams) (PartCategory, error) {
	row := q.db.QueryRow(ctx, createPartCategory, arg.Name, arg.Description)
	var i PartCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const createSupplier = `-- name: CreateSupplier :one
INSERT INTO suppliers (name, contact_person, phone, email, address, website, notes)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING id, name, contact_person, phone, email, address, website, notes, created_at, updated_at
`

type CreateSupplierParams struct {
	Name          string      `json:"name"`
	ContactPerson pgtype.Text `json:"contact_person"`
	Phone         pgtype.Text `json:"phone"`
	Email         pgtype.Text `json:"email"`
	Address       pgtype.Text `json:"address"`
	Website       pgtype.Text `json:"website"`
	Notes         pgtype.Text `json:"notes"`
}

func (q *Queries) CreateSupplier(ctx context.Context, arg CreateSupplierParams) (Supplier, error) {
	row := q.db.QueryRow(ctx, createSupplier,
		arg.Name,
		arg.ContactPerson,
		arg.Phone,
		arg.Email,
		arg.Address,
		arg.Website,
		arg.Notes,
	)
	var i Supplier
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ContactPerson,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Website,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deletePart = `-- name: DeletePart :exec
DELETE FROM parts
WHERE id = $1
`

func (q *Queries) DeletePart(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deletePart, id)
	return err
}

const deletePartCategory = `-- name: DeletePartCategory :exec
DELETE FROM part_categories
WHERE id = $1
`

func (q *Queries) DeletePartCategory(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deletePartCategory, id)
	return err
}

const deleteSupplier = `-- name: DeleteSupplier :exec
DELETE FROM suppliers
WHERE id = $1
`

func (q *Queries) DeleteSupplier(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteSupplier, id)
	return err
}

const getPart = `-- name: GetPart :one
SELECT p.id, p.category_id, p.supplier_id, p.name, p.description, p.part_number, p.brand, p.price, p.cost, p.quantity_in_stock, p.min_stock_level, p.is_active, p.created_at, p.updated_at, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.id = $1
`

type GetPartRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
	SupplierName    pgtype.Text        `json:"supplier_name"`
}

func (q *Queries) GetPart(ctx context.Context, id pgtype.UUID) (GetPartRow, error) {
	row := q.db.QueryRow(ctx, getPart, id)
	var i GetPartRow
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.SupplierID,
		&i.Name,
		&i.Description,
		&i.PartNumber,
		&i.Brand,
		&i.Price,
		&i.Cost,
		&i.QuantityInStock,
		&i.MinStockLevel,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CategoryName,
		&i.SupplierName,
	)
	return i, err
}

const getPartCategory = `-- name: GetPartCategory :one
SELECT id, name, description, created_at FROM part_categories
WHERE id = $1
`

func (q *Queries) GetPartCategory(ctx context.Context, id pgtype.UUID) (PartCategory, error) {
	row := q.db.QueryRow(ctx, getPartCategory, id)
	var i PartCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const getSupplier = `-- name: GetSupplier :one
SELECT id, name, contact_person, phone, email, address, website, notes, created_at, updated_at FROM suppliers
WHERE id = $1
`

func (q *Queries) GetSupplier(ctx context.Context, id pgtype.UUID) (Supplier, error) {
	row := q.db.QueryRow(ctx, getSupplier, id)
	var i Supplier
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ContactPerson,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Website,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getTaskParts = `-- name: GetTaskParts :many
SELECT tp.id, tp.task_id, tp.part_id, tp.quantity, tp.price, tp.notes, tp.created_at, p.name as part_name, p.description as part_description, p.part_number
FROM task_parts tp
JOIN parts p ON tp.part_id = p.id
WHERE tp.task_id = $1
ORDER BY tp.created_at
`

type GetTaskPartsRow struct {
	ID              pgtype.UUID        `json:"id"`
	TaskID          pgtype.UUID        `json:"task_id"`
	PartID          pgtype.UUID        `json:"part_id"`
	Quantity        int32              `json:"quantity"`
	Price           pgtype.Numeric     `json:"price"`
	Notes           pgtype.Text        `json:"notes"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	PartName        string             `json:"part_name"`
	PartDescription pgtype.Text        `json:"part_description"`
	PartNumber      pgtype.Text        `json:"part_number"`
}

func (q *Queries) GetTaskParts(ctx context.Context, taskID pgtype.UUID) ([]GetTaskPartsRow, error) {
	rows, err := q.db.Query(ctx, getTaskParts, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetTaskPartsRow{}
	for rows.Next() {
		var i GetTaskPartsRow
		if err := rows.Scan(
			&i.ID,
			&i.TaskID,
			&i.PartID,
			&i.Quantity,
			&i.Price,
			&i.Notes,
			&i.CreatedAt,
			&i.PartName,
			&i.PartDescription,
			&i.PartNumber,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listLowStockParts = `-- name: ListLowStockParts :many
SELECT p.id, p.category_id, p.supplier_id, p.name, p.description, p.part_number, p.brand, p.price, p.cost, p.quantity_in_stock, p.min_stock_level, p.is_active, p.created_at, p.updated_at, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true AND p.quantity_in_stock <= p.min_stock_level
ORDER BY p.quantity_in_stock ASC
`

type ListLowStockPartsRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
	SupplierName    pgtype.Text        `json:"supplier_name"`
}

func (q *Queries) ListLowStockParts(ctx context.Context) ([]ListLowStockPartsRow, error) {
	rows, err := q.db.Query(ctx, listLowStockParts)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListLowStockPartsRow{}
	for rows.Next() {
		var i ListLowStockPartsRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.SupplierID,
			&i.Name,
			&i.Description,
			&i.PartNumber,
			&i.Brand,
			&i.Price,
			&i.Cost,
			&i.QuantityInStock,
			&i.MinStockLevel,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
			&i.SupplierName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPartCategories = `-- name: ListPartCategories :many
SELECT id, name, description, created_at FROM part_categories
ORDER BY name
`

func (q *Queries) ListPartCategories(ctx context.Context) ([]PartCategory, error) {
	rows, err := q.db.Query(ctx, listPartCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []PartCategory{}
	for rows.Next() {
		var i PartCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listParts = `-- name: ListParts :many
SELECT p.id, p.category_id, p.supplier_id, p.name, p.description, p.part_number, p.brand, p.price, p.cost, p.quantity_in_stock, p.min_stock_level, p.is_active, p.created_at, p.updated_at, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true
ORDER BY pc.name, p.name
`

type ListPartsRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
	SupplierName    pgtype.Text        `json:"supplier_name"`
}

func (q *Queries) ListParts(ctx context.Context) ([]ListPartsRow, error) {
	rows, err := q.db.Query(ctx, listParts)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPartsRow{}
	for rows.Next() {
		var i ListPartsRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.SupplierID,
			&i.Name,
			&i.Description,
			&i.PartNumber,
			&i.Brand,
			&i.Price,
			&i.Cost,
			&i.QuantityInStock,
			&i.MinStockLevel,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
			&i.SupplierName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPartsByCategory = `-- name: ListPartsByCategory :many
SELECT p.id, p.category_id, p.supplier_id, p.name, p.description, p.part_number, p.brand, p.price, p.cost, p.quantity_in_stock, p.min_stock_level, p.is_active, p.created_at, p.updated_at, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.category_id = $1 AND p.is_active = true
ORDER BY p.name
`

type ListPartsByCategoryRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
	SupplierName    pgtype.Text        `json:"supplier_name"`
}

func (q *Queries) ListPartsByCategory(ctx context.Context, categoryID pgtype.UUID) ([]ListPartsByCategoryRow, error) {
	rows, err := q.db.Query(ctx, listPartsByCategory, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListPartsByCategoryRow{}
	for rows.Next() {
		var i ListPartsByCategoryRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.SupplierID,
			&i.Name,
			&i.Description,
			&i.PartNumber,
			&i.Brand,
			&i.Price,
			&i.Cost,
			&i.QuantityInStock,
			&i.MinStockLevel,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
			&i.SupplierName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listSuppliers = `-- name: ListSuppliers :many
SELECT id, name, contact_person, phone, email, address, website, notes, created_at, updated_at FROM suppliers
ORDER BY name
`

func (q *Queries) ListSuppliers(ctx context.Context) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, listSuppliers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Supplier{}
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ContactPerson,
			&i.Phone,
			&i.Email,
			&i.Address,
			&i.Website,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removePartFromTask = `-- name: RemovePartFromTask :exec
DELETE FROM task_parts
WHERE task_id = $1 AND part_id = $2
`

type RemovePartFromTaskParams struct {
	TaskID pgtype.UUID `json:"task_id"`
	PartID pgtype.UUID `json:"part_id"`
}

func (q *Queries) RemovePartFromTask(ctx context.Context, arg RemovePartFromTaskParams) error {
	_, err := q.db.Exec(ctx, removePartFromTask, arg.TaskID, arg.PartID)
	return err
}

const searchParts = `-- name: SearchParts :many
SELECT p.id, p.category_id, p.supplier_id, p.name, p.description, p.part_number, p.brand, p.price, p.cost, p.quantity_in_stock, p.min_stock_level, p.is_active, p.created_at, p.updated_at, pc.name as category_name, s.name as supplier_name
FROM parts p
LEFT JOIN part_categories pc ON p.category_id = pc.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.is_active = true
  AND (p.name ILIKE '%' || $1 || '%' 
       OR p.description ILIKE '%' || $1 || '%'
       OR p.part_number ILIKE '%' || $1 || '%'
       OR p.brand ILIKE '%' || $1 || '%')
ORDER BY p.name
`

type SearchPartsRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	SupplierID      pgtype.UUID        `json:"supplier_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	PartNumber      pgtype.Text        `json:"part_number"`
	Brand           pgtype.Text        `json:"brand"`
	Price           pgtype.Numeric     `json:"price"`
	Cost            pgtype.Numeric     `json:"cost"`
	QuantityInStock pgtype.Int4        `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4        `json:"min_stock_level"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
	SupplierName    pgtype.Text        `json:"supplier_name"`
}

func (q *Queries) SearchParts(ctx context.Context, dollar_1 pgtype.Text) ([]SearchPartsRow, error) {
	rows, err := q.db.Query(ctx, searchParts, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []SearchPartsRow{}
	for rows.Next() {
		var i SearchPartsRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.SupplierID,
			&i.Name,
			&i.Description,
			&i.PartNumber,
			&i.Brand,
			&i.Price,
			&i.Cost,
			&i.QuantityInStock,
			&i.MinStockLevel,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
			&i.SupplierName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchSuppliers = `-- name: SearchSuppliers :many
SELECT id, name, contact_person, phone, email, address, website, notes, created_at, updated_at FROM suppliers
WHERE name ILIKE '%' || $1 || '%'
   OR contact_person ILIKE '%' || $1 || '%'
   OR email ILIKE '%' || $1 || '%'
ORDER BY name
`

func (q *Queries) SearchSuppliers(ctx context.Context, dollar_1 pgtype.Text) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, searchSuppliers, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Supplier{}
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ContactPerson,
			&i.Phone,
			&i.Email,
			&i.Address,
			&i.Website,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updatePart = `-- name: UpdatePart :one
UPDATE parts
SET category_id = $2, supplier_id = $3, name = $4, description = $5, 
    part_number = $6, brand = $7, price = $8, cost = $9, 
    quantity_in_stock = $10, min_stock_level = $11, is_active = $12, updated_at = NOW()
WHERE id = $1
RETURNING id, category_id, supplier_id, name, description, part_number, brand, price, cost, quantity_in_stock, min_stock_level, is_active, created_at, updated_at
`

type UpdatePartParams struct {
	ID              pgtype.UUID    `json:"id"`
	CategoryID      pgtype.UUID    `json:"category_id"`
	SupplierID      pgtype.UUID    `json:"supplier_id"`
	Name            string         `json:"name"`
	Description     pgtype.Text    `json:"description"`
	PartNumber      pgtype.Text    `json:"part_number"`
	Brand           pgtype.Text    `json:"brand"`
	Price           pgtype.Numeric `json:"price"`
	Cost            pgtype.Numeric `json:"cost"`
	QuantityInStock pgtype.Int4    `json:"quantity_in_stock"`
	MinStockLevel   pgtype.Int4    `json:"min_stock_level"`
	IsActive        pgtype.Bool    `json:"is_active"`
}

func (q *Queries) UpdatePart(ctx context.Context, arg UpdatePartParams) (Part, error) {
	row := q.db.QueryRow(ctx, updatePart,
		arg.ID,
		arg.CategoryID,
		arg.SupplierID,
		arg.Name,
		arg.Description,
		arg.PartNumber,
		arg.Brand,
		arg.Price,
		arg.Cost,
		arg.QuantityInStock,
		arg.MinStockLevel,
		arg.IsActive,
	)
	var i Part
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.SupplierID,
		&i.Name,
		&i.Description,
		&i.PartNumber,
		&i.Brand,
		&i.Price,
		&i.Cost,
		&i.QuantityInStock,
		&i.MinStockLevel,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updatePartCategory = `-- name: UpdatePartCategory :one
UPDATE part_categories
SET name = $2, description = $3
WHERE id = $1
RETURNING id, name, description, created_at
`

type UpdatePartCategoryParams struct {
	ID          pgtype.UUID `json:"id"`
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) UpdatePartCategory(ctx context.Context, arg UpdatePartCategoryParams) (PartCategory, error) {
	row := q.db.QueryRow(ctx, updatePartCategory, arg.ID, arg.Name, arg.Description)
	var i PartCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const updatePartStock = `-- name: UpdatePartStock :one
UPDATE parts
SET quantity_in_stock = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, category_id, supplier_id, name, description, part_number, brand, price, cost, quantity_in_stock, min_stock_level, is_active, created_at, updated_at
`

type UpdatePartStockParams struct {
	ID              pgtype.UUID `json:"id"`
	QuantityInStock pgtype.Int4 `json:"quantity_in_stock"`
}

func (q *Queries) UpdatePartStock(ctx context.Context, arg UpdatePartStockParams) (Part, error) {
	row := q.db.QueryRow(ctx, updatePartStock, arg.ID, arg.QuantityInStock)
	var i Part
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.SupplierID,
		&i.Name,
		&i.Description,
		&i.PartNumber,
		&i.Brand,
		&i.Price,
		&i.Cost,
		&i.QuantityInStock,
		&i.MinStockLevel,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateSupplier = `-- name: UpdateSupplier :one
UPDATE suppliers
SET name = $2, contact_person = $3, phone = $4, email = $5, 
    address = $6, website = $7, notes = $8, updated_at = NOW()
WHERE id = $1
RETURNING id, name, contact_person, phone, email, address, website, notes, created_at, updated_at
`

type UpdateSupplierParams struct {
	ID            pgtype.UUID `json:"id"`
	Name          string      `json:"name"`
	ContactPerson pgtype.Text `json:"contact_person"`
	Phone         pgtype.Text `json:"phone"`
	Email         pgtype.Text `json:"email"`
	Address       pgtype.Text `json:"address"`
	Website       pgtype.Text `json:"website"`
	Notes         pgtype.Text `json:"notes"`
}

func (q *Queries) UpdateSupplier(ctx context.Context, arg UpdateSupplierParams) (Supplier, error) {
	row := q.db.QueryRow(ctx, updateSupplier,
		arg.ID,
		arg.Name,
		arg.ContactPerson,
		arg.Phone,
		arg.Email,
		arg.Address,
		arg.Website,
		arg.Notes,
	)
	var i Supplier
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ContactPerson,
		&i.Phone,
		&i.Email,
		&i.Address,
		&i.Website,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTaskPart = `-- name: UpdateTaskPart :one
UPDATE task_parts
SET quantity = $3, price = $4, notes = $5
WHERE task_id = $1 AND part_id = $2
RETURNING id, task_id, part_id, quantity, price, notes, created_at
`

type UpdateTaskPartParams struct {
	TaskID   pgtype.UUID    `json:"task_id"`
	PartID   pgtype.UUID    `json:"part_id"`
	Quantity int32          `json:"quantity"`
	Price    pgtype.Numeric `json:"price"`
	Notes    pgtype.Text    `json:"notes"`
}

func (q *Queries) UpdateTaskPart(ctx context.Context, arg UpdateTaskPartParams) (TaskPart, error) {
	row := q.db.QueryRow(ctx, updateTaskPart,
		arg.TaskID,
		arg.PartID,
		arg.Quantity,
		arg.Price,
		arg.Notes,
	)
	var i TaskPart
	err := row.Scan(
		&i.ID,
		&i.TaskID,
		&i.PartID,
		&i.Quantity,
		&i.Price,
		&i.Notes,
		&i.CreatedAt,
	)
	return i, err
}
