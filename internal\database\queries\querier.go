// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type Querier interface {
	AddPartToTask(ctx context.Context, arg AddPartToTaskParams) (TaskPart, error)
	AddServiceToTask(ctx context.Context, arg AddServiceToTaskParams) (TaskService, error)
	CompleteTask(ctx context.Context, id pgtype.UUID) (Task, error)
	CreateBicycle(ctx context.Context, arg CreateBicycleParams) (Bicycle, error)
	CreateClient(ctx context.Context, arg CreateClientParams) (Client, error)
	CreatePart(ctx context.Context, arg CreatePartParams) (Part, error)
	CreatePartCategory(ctx context.Context, arg CreatePartCategoryParams) (PartCategory, error)
	CreateService(ctx context.Context, arg CreateServiceParams) (Service, error)
	CreateServiceCategory(ctx context.Context, arg CreateServiceCategoryParams) (ServiceCategory, error)
	CreateSupplier(ctx context.Context, arg CreateSupplierParams) (Supplier, error)
	CreateTask(ctx context.Context, arg CreateTaskParams) (Task, error)
	DeleteBicycle(ctx context.Context, id pgtype.UUID) error
	DeleteClient(ctx context.Context, id pgtype.UUID) error
	DeletePart(ctx context.Context, id pgtype.UUID) error
	DeletePartCategory(ctx context.Context, id pgtype.UUID) error
	DeleteService(ctx context.Context, id pgtype.UUID) error
	DeleteServiceCategory(ctx context.Context, id pgtype.UUID) error
	DeleteSupplier(ctx context.Context, id pgtype.UUID) error
	DeleteTask(ctx context.Context, id pgtype.UUID) error
	GetBicycle(ctx context.Context, id pgtype.UUID) (Bicycle, error)
	GetBicyclesByClient(ctx context.Context, clientID pgtype.UUID) ([]Bicycle, error)
	GetClient(ctx context.Context, id pgtype.UUID) (Client, error)
	GetClientByPhone(ctx context.Context, phone pgtype.Text) (Client, error)
	GetPart(ctx context.Context, id pgtype.UUID) (GetPartRow, error)
	GetPartCategory(ctx context.Context, id pgtype.UUID) (PartCategory, error)
	GetService(ctx context.Context, id pgtype.UUID) (GetServiceRow, error)
	GetServiceCategory(ctx context.Context, id pgtype.UUID) (ServiceCategory, error)
	GetSupplier(ctx context.Context, id pgtype.UUID) (Supplier, error)
	GetTask(ctx context.Context, id pgtype.UUID) (GetTaskRow, error)
	GetTaskParts(ctx context.Context, taskID pgtype.UUID) ([]GetTaskPartsRow, error)
	GetTaskServices(ctx context.Context, taskID pgtype.UUID) ([]GetTaskServicesRow, error)
	ListBicycles(ctx context.Context) ([]ListBicyclesRow, error)
	ListClients(ctx context.Context) ([]Client, error)
	ListLowStockParts(ctx context.Context) ([]ListLowStockPartsRow, error)
	ListPartCategories(ctx context.Context) ([]PartCategory, error)
	ListParts(ctx context.Context) ([]ListPartsRow, error)
	ListPartsByCategory(ctx context.Context, categoryID pgtype.UUID) ([]ListPartsByCategoryRow, error)
	ListServiceCategories(ctx context.Context) ([]ServiceCategory, error)
	ListServices(ctx context.Context) ([]ListServicesRow, error)
	ListServicesByCategory(ctx context.Context, categoryID pgtype.UUID) ([]ListServicesByCategoryRow, error)
	ListSuppliers(ctx context.Context) ([]Supplier, error)
	ListTasks(ctx context.Context) ([]ListTasksRow, error)
	ListTasksByClient(ctx context.Context, clientID pgtype.UUID) ([]ListTasksByClientRow, error)
	ListTasksByStatus(ctx context.Context, status NullTaskStatus) ([]ListTasksByStatusRow, error)
	RemovePartFromTask(ctx context.Context, arg RemovePartFromTaskParams) error
	RemoveServiceFromTask(ctx context.Context, arg RemoveServiceFromTaskParams) error
	SearchBicycles(ctx context.Context, dollar_1 pgtype.Text) ([]SearchBicyclesRow, error)
	SearchClients(ctx context.Context, dollar_1 pgtype.Text) ([]Client, error)
	SearchParts(ctx context.Context, dollar_1 pgtype.Text) ([]SearchPartsRow, error)
	SearchServices(ctx context.Context, dollar_1 pgtype.Text) ([]SearchServicesRow, error)
	SearchSuppliers(ctx context.Context, dollar_1 pgtype.Text) ([]Supplier, error)
	SearchTasks(ctx context.Context, dollar_1 pgtype.Text) ([]SearchTasksRow, error)
	StartTask(ctx context.Context, id pgtype.UUID) (Task, error)
	UpdateBicycle(ctx context.Context, arg UpdateBicycleParams) (Bicycle, error)
	UpdateClient(ctx context.Context, arg UpdateClientParams) (Client, error)
	UpdatePart(ctx context.Context, arg UpdatePartParams) (Part, error)
	UpdatePartCategory(ctx context.Context, arg UpdatePartCategoryParams) (PartCategory, error)
	UpdatePartStock(ctx context.Context, arg UpdatePartStockParams) (Part, error)
	UpdateService(ctx context.Context, arg UpdateServiceParams) (Service, error)
	UpdateServiceCategory(ctx context.Context, arg UpdateServiceCategoryParams) (ServiceCategory, error)
	UpdateSupplier(ctx context.Context, arg UpdateSupplierParams) (Supplier, error)
	UpdateTask(ctx context.Context, arg UpdateTaskParams) (Task, error)
	UpdateTaskPart(ctx context.Context, arg UpdateTaskPartParams) (TaskPart, error)
	UpdateTaskService(ctx context.Context, arg UpdateTaskServiceParams) (TaskService, error)
	UpdateTaskStatus(ctx context.Context, arg UpdateTaskStatusParams) (Task, error)
}

var _ Querier = (*Queries)(nil)
