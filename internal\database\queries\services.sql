-- name: CreateServiceCategory :one
INSERT INTO service_categories (name, description)
VALUES ($1, $2)
RETURNING *;

-- name: GetServiceCategory :one
SELECT * FROM service_categories
WHERE id = $1;

-- name: ListServiceCategories :many
SELECT * FROM service_categories
ORDER BY name;

-- name: UpdateServiceCategory :one
UPDATE service_categories
SET name = $2, description = $3
WHERE id = $1
RETURNING *;

-- name: DeleteServiceCategory :exec
DELETE FROM service_categories
WHERE id = $1;

-- name: CreateService :one
INSERT INTO services (category_id, name, description, price, duration_minutes, is_active)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING *;

-- name: GetService :one
SELECT s.*, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.id = $1;

-- name: ListServices :many
SELECT s.*, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.is_active = true
ORDER BY sc.name, s.name;

-- name: ListServicesByCategory :many
SELECT s.*, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.category_id = $1 AND s.is_active = true
ORDER BY s.name;

-- name: SearchServices :many
SELECT s.*, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.is_active = true
  AND (s.name ILIKE '%' || $1 || '%' OR s.description ILIKE '%' || $1 || '%')
ORDER BY s.name;

-- name: UpdateService :one
UPDATE services
SET category_id = $2, name = $3, description = $4, price = $5, 
    duration_minutes = $6, is_active = $7, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteService :exec
DELETE FROM services
WHERE id = $1;

-- name: AddServiceToTask :one
INSERT INTO task_services (task_id, service_id, quantity, price, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: GetTaskServices :many
SELECT ts.*, s.name as service_name, s.description as service_description
FROM task_services ts
JOIN services s ON ts.service_id = s.id
WHERE ts.task_id = $1
ORDER BY ts.created_at;

-- name: UpdateTaskService :one
UPDATE task_services
SET quantity = $3, price = $4, notes = $5
WHERE task_id = $1 AND service_id = $2
RETURNING *;

-- name: RemoveServiceFromTask :exec
DELETE FROM task_services
WHERE task_id = $1 AND service_id = $2;
