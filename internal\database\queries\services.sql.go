// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: services.sql

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const addServiceToTask = `-- name: AddServiceToTask :one
INSERT INTO task_services (task_id, service_id, quantity, price, notes)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, task_id, service_id, quantity, price, notes, created_at
`

type AddServiceToTaskParams struct {
	TaskID    pgtype.UUID    `json:"task_id"`
	ServiceID pgtype.UUID    `json:"service_id"`
	Quantity  pgtype.Int4    `json:"quantity"`
	Price     pgtype.Numeric `json:"price"`
	Notes     pgtype.Text    `json:"notes"`
}

func (q *Queries) AddServiceToTask(ctx context.Context, arg AddServiceToTaskParams) (TaskService, error) {
	row := q.db.QueryRow(ctx, addServiceToTask,
		arg.TaskID,
		arg.ServiceID,
		arg.Quantity,
		arg.Price,
		arg.Notes,
	)
	var i TaskService
	err := row.Scan(
		&i.ID,
		&i.TaskID,
		&i.ServiceID,
		&i.Quantity,
		&i.Price,
		&i.Notes,
		&i.CreatedAt,
	)
	return i, err
}

const createService = `-- name: CreateService :one
INSERT INTO services (category_id, name, description, price, duration_minutes, is_active)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id, category_id, name, description, price, duration_minutes, is_active, created_at, updated_at
`

type CreateServiceParams struct {
	CategoryID      pgtype.UUID    `json:"category_id"`
	Name            string         `json:"name"`
	Description     pgtype.Text    `json:"description"`
	Price           pgtype.Numeric `json:"price"`
	DurationMinutes pgtype.Int4    `json:"duration_minutes"`
	IsActive        pgtype.Bool    `json:"is_active"`
}

func (q *Queries) CreateService(ctx context.Context, arg CreateServiceParams) (Service, error) {
	row := q.db.QueryRow(ctx, createService,
		arg.CategoryID,
		arg.Name,
		arg.Description,
		arg.Price,
		arg.DurationMinutes,
		arg.IsActive,
	)
	var i Service
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Name,
		&i.Description,
		&i.Price,
		&i.DurationMinutes,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createServiceCategory = `-- name: CreateServiceCategory :one
INSERT INTO service_categories (name, description)
VALUES ($1, $2)
RETURNING id, name, description, created_at
`

type CreateServiceCategoryParams struct {
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) CreateServiceCategory(ctx context.Context, arg CreateServiceCategoryParams) (ServiceCategory, error) {
	row := q.db.QueryRow(ctx, createServiceCategory, arg.Name, arg.Description)
	var i ServiceCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const deleteService = `-- name: DeleteService :exec
DELETE FROM services
WHERE id = $1
`

func (q *Queries) DeleteService(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteService, id)
	return err
}

const deleteServiceCategory = `-- name: DeleteServiceCategory :exec
DELETE FROM service_categories
WHERE id = $1
`

func (q *Queries) DeleteServiceCategory(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteServiceCategory, id)
	return err
}

const getService = `-- name: GetService :one
SELECT s.id, s.category_id, s.name, s.description, s.price, s.duration_minutes, s.is_active, s.created_at, s.updated_at, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.id = $1
`

type GetServiceRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	Price           pgtype.Numeric     `json:"price"`
	DurationMinutes pgtype.Int4        `json:"duration_minutes"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
}

func (q *Queries) GetService(ctx context.Context, id pgtype.UUID) (GetServiceRow, error) {
	row := q.db.QueryRow(ctx, getService, id)
	var i GetServiceRow
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Name,
		&i.Description,
		&i.Price,
		&i.DurationMinutes,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CategoryName,
	)
	return i, err
}

const getServiceCategory = `-- name: GetServiceCategory :one
SELECT id, name, description, created_at FROM service_categories
WHERE id = $1
`

func (q *Queries) GetServiceCategory(ctx context.Context, id pgtype.UUID) (ServiceCategory, error) {
	row := q.db.QueryRow(ctx, getServiceCategory, id)
	var i ServiceCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const getTaskServices = `-- name: GetTaskServices :many
SELECT ts.id, ts.task_id, ts.service_id, ts.quantity, ts.price, ts.notes, ts.created_at, s.name as service_name, s.description as service_description
FROM task_services ts
JOIN services s ON ts.service_id = s.id
WHERE ts.task_id = $1
ORDER BY ts.created_at
`

type GetTaskServicesRow struct {
	ID                 pgtype.UUID        `json:"id"`
	TaskID             pgtype.UUID        `json:"task_id"`
	ServiceID          pgtype.UUID        `json:"service_id"`
	Quantity           pgtype.Int4        `json:"quantity"`
	Price              pgtype.Numeric     `json:"price"`
	Notes              pgtype.Text        `json:"notes"`
	CreatedAt          pgtype.Timestamptz `json:"created_at"`
	ServiceName        string             `json:"service_name"`
	ServiceDescription pgtype.Text        `json:"service_description"`
}

func (q *Queries) GetTaskServices(ctx context.Context, taskID pgtype.UUID) ([]GetTaskServicesRow, error) {
	rows, err := q.db.Query(ctx, getTaskServices, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetTaskServicesRow{}
	for rows.Next() {
		var i GetTaskServicesRow
		if err := rows.Scan(
			&i.ID,
			&i.TaskID,
			&i.ServiceID,
			&i.Quantity,
			&i.Price,
			&i.Notes,
			&i.CreatedAt,
			&i.ServiceName,
			&i.ServiceDescription,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listServiceCategories = `-- name: ListServiceCategories :many
SELECT id, name, description, created_at FROM service_categories
ORDER BY name
`

func (q *Queries) ListServiceCategories(ctx context.Context) ([]ServiceCategory, error) {
	rows, err := q.db.Query(ctx, listServiceCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ServiceCategory{}
	for rows.Next() {
		var i ServiceCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listServices = `-- name: ListServices :many
SELECT s.id, s.category_id, s.name, s.description, s.price, s.duration_minutes, s.is_active, s.created_at, s.updated_at, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.is_active = true
ORDER BY sc.name, s.name
`

type ListServicesRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	Price           pgtype.Numeric     `json:"price"`
	DurationMinutes pgtype.Int4        `json:"duration_minutes"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
}

func (q *Queries) ListServices(ctx context.Context) ([]ListServicesRow, error) {
	rows, err := q.db.Query(ctx, listServices)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListServicesRow{}
	for rows.Next() {
		var i ListServicesRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.DurationMinutes,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listServicesByCategory = `-- name: ListServicesByCategory :many
SELECT s.id, s.category_id, s.name, s.description, s.price, s.duration_minutes, s.is_active, s.created_at, s.updated_at, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.category_id = $1 AND s.is_active = true
ORDER BY s.name
`

type ListServicesByCategoryRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	Price           pgtype.Numeric     `json:"price"`
	DurationMinutes pgtype.Int4        `json:"duration_minutes"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
}

func (q *Queries) ListServicesByCategory(ctx context.Context, categoryID pgtype.UUID) ([]ListServicesByCategoryRow, error) {
	rows, err := q.db.Query(ctx, listServicesByCategory, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListServicesByCategoryRow{}
	for rows.Next() {
		var i ListServicesByCategoryRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.DurationMinutes,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removeServiceFromTask = `-- name: RemoveServiceFromTask :exec
DELETE FROM task_services
WHERE task_id = $1 AND service_id = $2
`

type RemoveServiceFromTaskParams struct {
	TaskID    pgtype.UUID `json:"task_id"`
	ServiceID pgtype.UUID `json:"service_id"`
}

func (q *Queries) RemoveServiceFromTask(ctx context.Context, arg RemoveServiceFromTaskParams) error {
	_, err := q.db.Exec(ctx, removeServiceFromTask, arg.TaskID, arg.ServiceID)
	return err
}

const searchServices = `-- name: SearchServices :many
SELECT s.id, s.category_id, s.name, s.description, s.price, s.duration_minutes, s.is_active, s.created_at, s.updated_at, sc.name as category_name
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
WHERE s.is_active = true
  AND (s.name ILIKE '%' || $1 || '%' OR s.description ILIKE '%' || $1 || '%')
ORDER BY s.name
`

type SearchServicesRow struct {
	ID              pgtype.UUID        `json:"id"`
	CategoryID      pgtype.UUID        `json:"category_id"`
	Name            string             `json:"name"`
	Description     pgtype.Text        `json:"description"`
	Price           pgtype.Numeric     `json:"price"`
	DurationMinutes pgtype.Int4        `json:"duration_minutes"`
	IsActive        pgtype.Bool        `json:"is_active"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	UpdatedAt       pgtype.Timestamptz `json:"updated_at"`
	CategoryName    pgtype.Text        `json:"category_name"`
}

func (q *Queries) SearchServices(ctx context.Context, dollar_1 pgtype.Text) ([]SearchServicesRow, error) {
	rows, err := q.db.Query(ctx, searchServices, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []SearchServicesRow{}
	for rows.Next() {
		var i SearchServicesRow
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.DurationMinutes,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CategoryName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateService = `-- name: UpdateService :one
UPDATE services
SET category_id = $2, name = $3, description = $4, price = $5, 
    duration_minutes = $6, is_active = $7, updated_at = NOW()
WHERE id = $1
RETURNING id, category_id, name, description, price, duration_minutes, is_active, created_at, updated_at
`

type UpdateServiceParams struct {
	ID              pgtype.UUID    `json:"id"`
	CategoryID      pgtype.UUID    `json:"category_id"`
	Name            string         `json:"name"`
	Description     pgtype.Text    `json:"description"`
	Price           pgtype.Numeric `json:"price"`
	DurationMinutes pgtype.Int4    `json:"duration_minutes"`
	IsActive        pgtype.Bool    `json:"is_active"`
}

func (q *Queries) UpdateService(ctx context.Context, arg UpdateServiceParams) (Service, error) {
	row := q.db.QueryRow(ctx, updateService,
		arg.ID,
		arg.CategoryID,
		arg.Name,
		arg.Description,
		arg.Price,
		arg.DurationMinutes,
		arg.IsActive,
	)
	var i Service
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Name,
		&i.Description,
		&i.Price,
		&i.DurationMinutes,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateServiceCategory = `-- name: UpdateServiceCategory :one
UPDATE service_categories
SET name = $2, description = $3
WHERE id = $1
RETURNING id, name, description, created_at
`

type UpdateServiceCategoryParams struct {
	ID          pgtype.UUID `json:"id"`
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
}

func (q *Queries) UpdateServiceCategory(ctx context.Context, arg UpdateServiceCategoryParams) (ServiceCategory, error) {
	row := q.db.QueryRow(ctx, updateServiceCategory, arg.ID, arg.Name, arg.Description)
	var i ServiceCategory
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const updateTaskService = `-- name: UpdateTaskService :one
UPDATE task_services
SET quantity = $3, price = $4, notes = $5
WHERE task_id = $1 AND service_id = $2
RETURNING id, task_id, service_id, quantity, price, notes, created_at
`

type UpdateTaskServiceParams struct {
	TaskID    pgtype.UUID    `json:"task_id"`
	ServiceID pgtype.UUID    `json:"service_id"`
	Quantity  pgtype.Int4    `json:"quantity"`
	Price     pgtype.Numeric `json:"price"`
	Notes     pgtype.Text    `json:"notes"`
}

func (q *Queries) UpdateTaskService(ctx context.Context, arg UpdateTaskServiceParams) (TaskService, error) {
	row := q.db.QueryRow(ctx, updateTaskService,
		arg.TaskID,
		arg.ServiceID,
		arg.Quantity,
		arg.Price,
		arg.Notes,
	)
	var i TaskService
	err := row.Scan(
		&i.ID,
		&i.TaskID,
		&i.ServiceID,
		&i.Quantity,
		&i.Price,
		&i.Notes,
		&i.CreatedAt,
	)
	return i, err
}
