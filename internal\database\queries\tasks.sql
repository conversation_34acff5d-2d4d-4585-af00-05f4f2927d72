-- name: CreateTask :one
INSERT INTO tasks (client_id, bicycle_id, title, description, status, priority, estimated_cost, estimated_completion, notes)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
RETURNING *;

-- name: GetTask :one
SELECT t.*, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.id = $1;

-- name: ListTasks :many
SELECT t.*, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
ORDER BY t.created_at DESC;

-- name: ListTasksByStatus :many
SELECT t.*, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.status = $1
ORDER BY t.priority DESC, t.created_at ASC;

-- name: ListTasksByClient :many
SELECT t.*, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.client_id = $1
ORDER BY t.created_at DESC;

-- name: SearchTasks :many
SELECT t.*, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.title ILIKE '%' || $1 || '%'
   OR t.description ILIKE '%' || $1 || '%'
   OR c.name ILIKE '%' || $1 || '%'
ORDER BY t.created_at DESC;

-- name: UpdateTask :one
UPDATE tasks
SET title = $2, description = $3, status = $4, priority = $5, 
    estimated_cost = $6, actual_cost = $7, estimated_completion = $8, 
    notes = $9, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: UpdateTaskStatus :one
UPDATE tasks
SET status = $2, updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: StartTask :one
UPDATE tasks
SET status = 'in_progress', started_at = NOW(), updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: CompleteTask :one
UPDATE tasks
SET status = 'completed', completed_at = NOW(), updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: DeleteTask :exec
DELETE FROM tasks
WHERE id = $1;
