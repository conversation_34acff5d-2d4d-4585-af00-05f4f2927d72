// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: tasks.sql

package queries

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const completeTask = `-- name: CompleteTask :one
UPDATE tasks
SET status = 'completed', completed_at = NOW(), updated_at = NOW()
WHERE id = $1
RETURNING id, client_id, bicycle_id, title, description, status, priority, estimated_cost, actual_cost, estimated_completion, started_at, completed_at, notes, created_at, updated_at
`

func (q *Queries) CompleteTask(ctx context.Context, id pgtype.UUID) (Task, error) {
	row := q.db.QueryRow(ctx, completeTask, id)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createTask = `-- name: CreateTask :one
INSERT INTO tasks (client_id, bicycle_id, title, description, status, priority, estimated_cost, estimated_completion, notes)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
RETURNING id, client_id, bicycle_id, title, description, status, priority, estimated_cost, actual_cost, estimated_completion, started_at, completed_at, notes, created_at, updated_at
`

type CreateTaskParams struct {
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	Notes               pgtype.Text        `json:"notes"`
}

func (q *Queries) CreateTask(ctx context.Context, arg CreateTaskParams) (Task, error) {
	row := q.db.QueryRow(ctx, createTask,
		arg.ClientID,
		arg.BicycleID,
		arg.Title,
		arg.Description,
		arg.Status,
		arg.Priority,
		arg.EstimatedCost,
		arg.EstimatedCompletion,
		arg.Notes,
	)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteTask = `-- name: DeleteTask :exec
DELETE FROM tasks
WHERE id = $1
`

func (q *Queries) DeleteTask(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteTask, id)
	return err
}

const getTask = `-- name: GetTask :one
SELECT t.id, t.client_id, t.bicycle_id, t.title, t.description, t.status, t.priority, t.estimated_cost, t.actual_cost, t.estimated_completion, t.started_at, t.completed_at, t.notes, t.created_at, t.updated_at, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.id = $1
`

type GetTaskRow struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ClientName          string             `json:"client_name"`
	BicycleBrand        pgtype.Text        `json:"bicycle_brand"`
	BicycleModel        pgtype.Text        `json:"bicycle_model"`
}

func (q *Queries) GetTask(ctx context.Context, id pgtype.UUID) (GetTaskRow, error) {
	row := q.db.QueryRow(ctx, getTask, id)
	var i GetTaskRow
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ClientName,
		&i.BicycleBrand,
		&i.BicycleModel,
	)
	return i, err
}

const listTasks = `-- name: ListTasks :many
SELECT t.id, t.client_id, t.bicycle_id, t.title, t.description, t.status, t.priority, t.estimated_cost, t.actual_cost, t.estimated_completion, t.started_at, t.completed_at, t.notes, t.created_at, t.updated_at, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
ORDER BY t.created_at DESC
`

type ListTasksRow struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ClientName          string             `json:"client_name"`
	BicycleBrand        pgtype.Text        `json:"bicycle_brand"`
	BicycleModel        pgtype.Text        `json:"bicycle_model"`
}

func (q *Queries) ListTasks(ctx context.Context) ([]ListTasksRow, error) {
	rows, err := q.db.Query(ctx, listTasks)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListTasksRow{}
	for rows.Next() {
		var i ListTasksRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.BicycleID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.EstimatedCost,
			&i.ActualCost,
			&i.EstimatedCompletion,
			&i.StartedAt,
			&i.CompletedAt,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
			&i.BicycleBrand,
			&i.BicycleModel,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTasksByClient = `-- name: ListTasksByClient :many
SELECT t.id, t.client_id, t.bicycle_id, t.title, t.description, t.status, t.priority, t.estimated_cost, t.actual_cost, t.estimated_completion, t.started_at, t.completed_at, t.notes, t.created_at, t.updated_at, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.client_id = $1
ORDER BY t.created_at DESC
`

type ListTasksByClientRow struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ClientName          string             `json:"client_name"`
	BicycleBrand        pgtype.Text        `json:"bicycle_brand"`
	BicycleModel        pgtype.Text        `json:"bicycle_model"`
}

func (q *Queries) ListTasksByClient(ctx context.Context, clientID pgtype.UUID) ([]ListTasksByClientRow, error) {
	rows, err := q.db.Query(ctx, listTasksByClient, clientID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListTasksByClientRow{}
	for rows.Next() {
		var i ListTasksByClientRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.BicycleID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.EstimatedCost,
			&i.ActualCost,
			&i.EstimatedCompletion,
			&i.StartedAt,
			&i.CompletedAt,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
			&i.BicycleBrand,
			&i.BicycleModel,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTasksByStatus = `-- name: ListTasksByStatus :many
SELECT t.id, t.client_id, t.bicycle_id, t.title, t.description, t.status, t.priority, t.estimated_cost, t.actual_cost, t.estimated_completion, t.started_at, t.completed_at, t.notes, t.created_at, t.updated_at, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.status = $1
ORDER BY t.priority DESC, t.created_at ASC
`

type ListTasksByStatusRow struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ClientName          string             `json:"client_name"`
	BicycleBrand        pgtype.Text        `json:"bicycle_brand"`
	BicycleModel        pgtype.Text        `json:"bicycle_model"`
}

func (q *Queries) ListTasksByStatus(ctx context.Context, status NullTaskStatus) ([]ListTasksByStatusRow, error) {
	rows, err := q.db.Query(ctx, listTasksByStatus, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListTasksByStatusRow{}
	for rows.Next() {
		var i ListTasksByStatusRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.BicycleID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.EstimatedCost,
			&i.ActualCost,
			&i.EstimatedCompletion,
			&i.StartedAt,
			&i.CompletedAt,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
			&i.BicycleBrand,
			&i.BicycleModel,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchTasks = `-- name: SearchTasks :many
SELECT t.id, t.client_id, t.bicycle_id, t.title, t.description, t.status, t.priority, t.estimated_cost, t.actual_cost, t.estimated_completion, t.started_at, t.completed_at, t.notes, t.created_at, t.updated_at, c.name as client_name, b.brand as bicycle_brand, b.model as bicycle_model
FROM tasks t
JOIN clients c ON t.client_id = c.id
LEFT JOIN bicycles b ON t.bicycle_id = b.id
WHERE t.title ILIKE '%' || $1 || '%'
   OR t.description ILIKE '%' || $1 || '%'
   OR c.name ILIKE '%' || $1 || '%'
ORDER BY t.created_at DESC
`

type SearchTasksRow struct {
	ID                  pgtype.UUID        `json:"id"`
	ClientID            pgtype.UUID        `json:"client_id"`
	BicycleID           pgtype.UUID        `json:"bicycle_id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	StartedAt           pgtype.Timestamptz `json:"started_at"`
	CompletedAt         pgtype.Timestamptz `json:"completed_at"`
	Notes               pgtype.Text        `json:"notes"`
	CreatedAt           pgtype.Timestamptz `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ClientName          string             `json:"client_name"`
	BicycleBrand        pgtype.Text        `json:"bicycle_brand"`
	BicycleModel        pgtype.Text        `json:"bicycle_model"`
}

func (q *Queries) SearchTasks(ctx context.Context, dollar_1 pgtype.Text) ([]SearchTasksRow, error) {
	rows, err := q.db.Query(ctx, searchTasks, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []SearchTasksRow{}
	for rows.Next() {
		var i SearchTasksRow
		if err := rows.Scan(
			&i.ID,
			&i.ClientID,
			&i.BicycleID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.EstimatedCost,
			&i.ActualCost,
			&i.EstimatedCompletion,
			&i.StartedAt,
			&i.CompletedAt,
			&i.Notes,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientName,
			&i.BicycleBrand,
			&i.BicycleModel,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const startTask = `-- name: StartTask :one
UPDATE tasks
SET status = 'in_progress', started_at = NOW(), updated_at = NOW()
WHERE id = $1
RETURNING id, client_id, bicycle_id, title, description, status, priority, estimated_cost, actual_cost, estimated_completion, started_at, completed_at, notes, created_at, updated_at
`

func (q *Queries) StartTask(ctx context.Context, id pgtype.UUID) (Task, error) {
	row := q.db.QueryRow(ctx, startTask, id)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTask = `-- name: UpdateTask :one
UPDATE tasks
SET title = $2, description = $3, status = $4, priority = $5, 
    estimated_cost = $6, actual_cost = $7, estimated_completion = $8, 
    notes = $9, updated_at = NOW()
WHERE id = $1
RETURNING id, client_id, bicycle_id, title, description, status, priority, estimated_cost, actual_cost, estimated_completion, started_at, completed_at, notes, created_at, updated_at
`

type UpdateTaskParams struct {
	ID                  pgtype.UUID        `json:"id"`
	Title               string             `json:"title"`
	Description         pgtype.Text        `json:"description"`
	Status              NullTaskStatus     `json:"status"`
	Priority            NullTaskPriority   `json:"priority"`
	EstimatedCost       pgtype.Numeric     `json:"estimated_cost"`
	ActualCost          pgtype.Numeric     `json:"actual_cost"`
	EstimatedCompletion pgtype.Timestamptz `json:"estimated_completion"`
	Notes               pgtype.Text        `json:"notes"`
}

func (q *Queries) UpdateTask(ctx context.Context, arg UpdateTaskParams) (Task, error) {
	row := q.db.QueryRow(ctx, updateTask,
		arg.ID,
		arg.Title,
		arg.Description,
		arg.Status,
		arg.Priority,
		arg.EstimatedCost,
		arg.ActualCost,
		arg.EstimatedCompletion,
		arg.Notes,
	)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTaskStatus = `-- name: UpdateTaskStatus :one
UPDATE tasks
SET status = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, client_id, bicycle_id, title, description, status, priority, estimated_cost, actual_cost, estimated_completion, started_at, completed_at, notes, created_at, updated_at
`

type UpdateTaskStatusParams struct {
	ID     pgtype.UUID    `json:"id"`
	Status NullTaskStatus `json:"status"`
}

func (q *Queries) UpdateTaskStatus(ctx context.Context, arg UpdateTaskStatusParams) (Task, error) {
	row := q.db.QueryRow(ctx, updateTaskStatus, arg.ID, arg.Status)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.ClientID,
		&i.BicycleID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.EstimatedCost,
		&i.ActualCost,
		&i.EstimatedCompletion,
		&i.StartedAt,
		&i.CompletedAt,
		&i.Notes,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
