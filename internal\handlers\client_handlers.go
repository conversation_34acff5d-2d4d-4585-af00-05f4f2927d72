package handlers

import (
	"velomasters/internal/database/queries"
	"velomasters/internal/models"
	"velomasters/internal/services"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// ListClients обрабатывает GET /api/clients
func (h *Handlers) ListClients(c *fiber.Ctx) error {
	clients, err := h.services.Client.ListClients(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list clients")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderClientsHTML(c, clients)
	}

	return c.JSON(clients)
}

// GetClient обрабатывает GET /api/clients/:id
func (h *Handlers) GetClient(c *fiber.Ctx) error {
	idStr := c.<PERSON>ms("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid client ID",
		})
	}

	client, err := h.services.Client.GetClient(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to get client")
	}

	return c.JSON(client)
}

// CreateClient обрабатывает POST /api/clients
func (h *Handlers) CreateClient(c *fiber.Ctx) error {
	var req services.CreateClientRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	client, err := h.services.Client.CreateClient(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create client")
	}

	return c.Status(fiber.StatusCreated).JSON(client)
}

// UpdateClient обрабатывает PUT /api/clients/:id
func (h *Handlers) UpdateClient(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid client ID",
		})
	}

	var req services.UpdateClientRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	client, err := h.services.Client.UpdateClient(c.Context(), id, req)
	if err != nil {
		return h.handleError(c, err, "Failed to update client")
	}

	return c.JSON(client)
}

// DeleteClient обрабатывает DELETE /api/clients/:id
func (h *Handlers) DeleteClient(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid client ID",
		})
	}

	err = h.services.Client.DeleteClient(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to delete client")
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// SearchClients обрабатывает GET /api/clients/search?q=query
func (h *Handlers) SearchClients(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Search query is required",
		})
	}

	clients, err := h.services.Client.SearchClients(c.Context(), query)
	if err != nil {
		return h.handleError(c, err, "Failed to search clients")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderClientsHTML(c, clients)
	}

	return c.JSON(clients)
}

// renderClientsHTML рендерит HTML для списка клиентов
func (h *Handlers) renderClientsHTML(c *fiber.Ctx, clients []queries.Client) error {
	html := `<div class="space-y-4">`

	if len(clients) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Клиенты не найдены</p>`
	} else {
		for _, client := range clients {
			clientID := models.ConvertPgTypeUUID(client.ID).String()
			phone := ""
			if client.Phone.Valid {
				phone = client.Phone.String
			}
			email := ""
			if client.Email.Valid {
				email = client.Email.String
			}

			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<div class="flex justify-between items-start">
						<div>
							<h3 class="text-lg font-semibold">` + client.Name + `</h3>
							<div class="text-sm text-gray-600 space-y-1">
								<p><span class="font-medium">Телефон:</span> ` + phone + `</p>
								<p><span class="font-medium">Email:</span> ` + email + `</p>
							</div>
						</div>
						<div class="flex space-x-2">
							<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
									hx-get="/api/clients/` + clientID + `"
									hx-target="#client-details">
								Просмотр
							</button>
							<button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
									hx-delete="/api/clients/` + clientID + `"
									hx-confirm="Вы уверены, что хотите удалить этого клиента?"
									hx-target="closest .border">
								Удалить
							</button>
						</div>
					</div>
				</div>
			`
		}
	}

	html += `</div>`

	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}
