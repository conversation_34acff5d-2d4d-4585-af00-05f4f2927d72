package handlers

import (
	"log"

	"velomasters/internal/services"
	"velomasters/web/templates/pages"

	"github.com/a-h/templ"
	"github.com/gofiber/fiber/v2"
)

// Handlers содержит все HTTP обработчики
type Handlers struct {
	services *services.Services
}

// NewHandlers создает новый экземпляр Handlers
func NewHandlers(services *services.Services) *Handlers {
	return &Handlers{
		services: services,
	}
}

// SetupRoutes настраивает маршруты приложения
func (h *Handlers) SetupRoutes(app *fiber.App) {
	// Статические файлы
	app.Static("/static", "./web/static")

	// Главная страница
	app.Get("/", h.HomePage)

	// API маршруты
	api := app.Group("/api")

	// Клиенты
	clients := api.Group("/clients")
	clients.Get("/", h.ListClients)
	clients.Post("/", h.CreateClient)
	clients.Get("/:id", h.GetClient)
	clients.Put("/:id", h.UpdateClient)
	clients.Delete("/:id", h.DeleteClient)
	clients.Get("/search", h.SearchClients)

	// Задачи
	tasks := api.Group("/tasks")
	tasks.Get("/", h.ListTasks)
	tasks.Post("/", h.CreateTask)
	tasks.Get("/:id", h.GetTask)
	tasks.Put("/:id", h.UpdateTask)
	tasks.Delete("/:id", h.DeleteTask)
	tasks.Get("/search", h.SearchTasks)
	tasks.Post("/:id/start", h.StartTask)
	tasks.Post("/:id/complete", h.CompleteTask)
	tasks.Get("/status/:status", h.ListTasksByStatus)

	// Услуги
	servicesGroup := api.Group("/services")
	servicesGroup.Get("/", h.ListServices)
	servicesGroup.Post("/", h.CreateService)
	servicesGroup.Get("/:id", h.GetService)
	servicesGroup.Put("/:id", h.UpdateService)
	servicesGroup.Delete("/:id", h.DeleteService)
	servicesGroup.Get("/search", h.SearchServices)

	// Категории услуг
	serviceCategories := api.Group("/service-categories")
	serviceCategories.Get("/", h.ListServiceCategories)
	serviceCategories.Post("/", h.CreateServiceCategory)

	// Запчасти
	parts := api.Group("/parts")
	parts.Get("/", h.ListParts)
	parts.Post("/", h.CreatePart)
	parts.Get("/:id", h.GetPart)
	parts.Put("/:id", h.UpdatePart)
	parts.Delete("/:id", h.DeletePart)
	parts.Get("/search", h.SearchParts)
	parts.Get("/category/:id", h.ListPartsByCategory)
	parts.Get("/low-stock", h.ListLowStockParts)
	parts.Patch("/:id/stock", h.UpdatePartStock)

	// Категории запчастей
	partCategories := api.Group("/part-categories")
	partCategories.Get("/", h.ListPartCategories)
	partCategories.Post("/", h.CreatePartCategory)

	// Поставщики
	suppliers := api.Group("/suppliers")
	suppliers.Get("/", h.ListSuppliers)
	suppliers.Post("/", h.CreateSupplier)
	suppliers.Get("/search", h.SearchSuppliers)

	// HTMX API маршруты для компонентов
	htmx := api.Group("/htmx")

	// HTMX задачи
	htmxTasks := htmx.Group("/tasks")
	htmxTasks.Get("/", h.HTMXListTasks)
	htmxTasks.Get("/form/:id", h.HTMXTaskForm)
	htmxTasks.Get("/form", h.HTMXTaskForm)
	htmxTasks.Post("/", h.HTMXCreateTask)
	htmxTasks.Patch("/:id/status", h.HTMXUpdateTaskStatus)

	// HTMX клиенты
	htmxClients := htmx.Group("/clients")
	htmxClients.Get("/", h.HTMXListClients)
	htmxClients.Get("/form/:id", h.HTMXClientForm)
	htmxClients.Get("/form", h.HTMXClientForm)
	htmxClients.Post("/", h.HTMXCreateClient)

	// HTMX услуги
	htmxServices := htmx.Group("/services")
	htmxServices.Get("/", h.HTMXListServices)
	htmxServices.Get("/form/:id", h.HTMXServiceForm)
	htmxServices.Get("/form", h.HTMXServiceForm)
	htmxServices.Post("/", h.HTMXCreateService)

	// HTMX запчасти
	htmxParts := htmx.Group("/parts")
	htmxParts.Get("/", h.HTMXListParts)
	htmxParts.Get("/:id/form", h.HTMXPartForm)
	htmxParts.Get("/new/form", h.HTMXPartForm)
	htmxParts.Post("/", h.HTMXCreatePart)

	// HTMX уведомления
	htmx.Get("/notification", h.HTMXNotification)

	// Веб-страницы
	web := app.Group("/web")
	web.Get("/tasks", h.TasksPage)
	web.Get("/clients", h.ClientsPage)
	web.Get("/services", h.ServicesPage)
	web.Get("/parts", h.PartsPage)
}

// HomePage обрабатывает главную страницу
func (h *Handlers) HomePage(c *fiber.Ctx) error {
	return renderTempl(c, pages.Home())
}

// TasksPage отображает страницу задач
func (h *Handlers) TasksPage(c *fiber.Ctx) error {
	return renderTempl(c, pages.Tasks())
}

// ClientsPage отображает страницу клиентов
func (h *Handlers) ClientsPage(c *fiber.Ctx) error {
	return renderTempl(c, pages.Clients())
}

// ServicesPage отображает страницу услуг
func (h *Handlers) ServicesPage(c *fiber.Ctx) error {
	return renderTempl(c, pages.Services())
}

// PartsPage отображает страницу запчастей
func (h *Handlers) PartsPage(c *fiber.Ctx) error {
	return renderTempl(c, pages.Parts())
}

// renderTempl рендерит Templ компонент
func renderTempl(c *fiber.Ctx, component templ.Component) error {
	c.Set("Content-Type", "text/html")
	return component.Render(c.Context(), c.Response().BodyWriter())
}

// handleError обрабатывает ошибки
func (h *Handlers) handleError(c *fiber.Ctx, err error, message string) error {
	log.Printf("Error: %s - %v", message, err)
	return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
		"error": message,
	})
}
