package handlers

import (
	"fmt"
	"velomasters/internal/models"
)

// getStatusDisplay возвращает отображение статуса задачи
func getStatusDisplay(status string) models.TaskStatusDisplay {
	statuses := models.GetTaskStatuses()
	for _, s := range statuses {
		if s.Value == status {
			return s
		}
	}
	return models.TaskStatusDisplay{Value: status, Label: status, Color: "gray"}
}

// getPriorityDisplay возвращает отображение приоритета задачи
func getPriorityDisplay(priority string) models.TaskPriorityDisplay {
	priorities := models.GetTaskPriorities()
	for _, p := range priorities {
		if p.Value == priority {
			return p
		}
	}
	return models.TaskPriorityDisplay{Value: priority, Label: priority, Color: "gray"}
}

// formatFloat форматирует float64 в строку
func formatFloat(f float64) string {
	return fmt.Sprintf("%.2f", f)
}

// formatInt форматирует int32 в строку
func formatInt(i int32) string {
	return fmt.Sprintf("%d", i)
}
