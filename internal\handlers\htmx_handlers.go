package handlers

import (
	"context"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"velomasters/internal/database/queries"
	"velomasters/web/templates/components"
)

// HTMXListTasks возвращает HTML список задач для HTMX
func (h *Handlers) HTMXListTasks(c *fiber.Ctx) error {
	// Получаем параметры фильтрации
	status := c.Query("status")
	priority := c.Query("priority")
	search := c.Query("search")

	// Получаем задачи с фильтрацией
	tasks, err := h.getFilteredTasks(c.Context(), status, priority, search)
	if err != nil {
		return h.renderError(c, "Ошибка загрузки задач")
	}

	// Рендерим компонент списка задач
	return h.renderComponent(c, components.TaskList(tasks, true))
}

// HTMXTaskForm возвращает форму создания/редактирования задачи
func (h *Handlers) HTMXTaskForm(c *fiber.Ctx) error {
	taskID := c.Params("id")

	// Получаем список клиентов для формы
	clients, err := h.getClients(c.Context())
	if err != nil {
		return h.renderError(c, "Ошибка загрузки клиентов")
	}

	if taskID == "" || taskID == "new" {
		// Форма создания новой задачи
		return h.renderComponent(c, components.TaskForm(nil, clients, false))
	}

	// Форма редактирования существующей задачи
	id, err := uuid.Parse(taskID)
	if err != nil {
		return h.renderError(c, "Неверный ID задачи")
	}

	task, err := h.getTask(c.Context(), id)
	if err != nil {
		return h.renderError(c, "Задача не найдена")
	}

	return h.renderComponent(c, components.TaskForm(&task, clients, true))
}

// HTMXCreateTask создает новую задачу и возвращает карточку
func (h *Handlers) HTMXCreateTask(c *fiber.Ctx) error {
	// Парсим данные формы
	title := c.FormValue("title")
	description := c.FormValue("description")
	clientIDStr := c.FormValue("client_id")
	priority := c.FormValue("priority")
	estimatedCostStr := c.FormValue("estimated_cost")
	dueDateStr := c.FormValue("due_date")

	// Валидация
	if title == "" || clientIDStr == "" {
		return h.renderError(c, "Название и клиент обязательны")
	}

	clientID, err := uuid.Parse(clientIDStr)
	if err != nil {
		return h.renderError(c, "Неверный ID клиента")
	}

	// Создаем задачу
	task, err := h.createTask(c.Context(), title, description, clientID, priority, estimatedCostStr, dueDateStr)
	if err != nil {
		return h.renderError(c, "Ошибка создания задачи")
	}

	// Возвращаем карточку новой задачи
	return h.renderComponent(c, components.TaskCard(task))
}

// HTMXUpdateTaskStatus обновляет статус задачи
func (h *Handlers) HTMXUpdateTaskStatus(c *fiber.Ctx) error {
	taskID := c.Params("id")
	action := c.FormValue("status") // "next" для перехода к следующему статусу

	id, err := uuid.Parse(taskID)
	if err != nil {
		return h.renderError(c, "Неверный ID задачи")
	}

	// Обновляем статус задачи
	task, err := h.updateTaskStatus(c.Context(), id, action)
	if err != nil {
		return h.renderError(c, "Ошибка обновления статуса")
	}

	// Возвращаем обновленную карточку
	return h.renderComponent(c, components.TaskCard(task))
}

// HTMXListClients возвращает HTML список клиентов для HTMX
func (h *Handlers) HTMXListClients(c *fiber.Ctx) error {
	search := c.Query("search")

	clients, err := h.getFilteredClients(c.Context(), search)
	if err != nil {
		return h.renderError(c, "Ошибка загрузки клиентов")
	}

	return h.renderComponent(c, components.ClientList(clients, true))
}

// HTMXClientForm возвращает форму создания/редактирования клиента
func (h *Handlers) HTMXClientForm(c *fiber.Ctx) error {
	clientID := c.Params("id")

	if clientID == "" || clientID == "new" {
		return h.renderComponent(c, components.ClientForm(nil, false))
	}

	id, err := uuid.Parse(clientID)
	if err != nil {
		return h.renderError(c, "Неверный ID клиента")
	}

	client, err := h.getClient(c.Context(), id)
	if err != nil {
		return h.renderError(c, "Клиент не найден")
	}

	return h.renderComponent(c, components.ClientForm(&client, true))
}

// HTMXCreateClient создает нового клиента
func (h *Handlers) HTMXCreateClient(c *fiber.Ctx) error {
	name := c.FormValue("name")
	phone := c.FormValue("phone")
	email := c.FormValue("email")
	address := c.FormValue("address")
	notes := c.FormValue("notes")

	if name == "" || phone == "" {
		return h.renderError(c, "Имя и телефон обязательны")
	}

	client, err := h.createClient(c.Context(), name, phone, email, address, notes)
	if err != nil {
		return h.renderError(c, "Ошибка создания клиента")
	}

	return h.renderComponent(c, components.ClientCard(client))
}

// HTMXListServices возвращает HTML список услуг для HTMX
func (h *Handlers) HTMXListServices(c *fiber.Ctx) error {
	category := c.Query("category")
	status := c.Query("status")
	search := c.Query("search")

	services, err := h.getFilteredServices(c.Context(), category, status, search)
	if err != nil {
		return h.renderError(c, "Ошибка загрузки услуг")
	}

	return h.renderComponent(c, components.ServiceList(services, true))
}

// HTMXServiceForm возвращает форму создания/редактирования услуги
func (h *Handlers) HTMXServiceForm(c *fiber.Ctx) error {
	serviceID := c.Params("id")
	categories := []string{"Ремонт", "Обслуживание", "Настройка", "Сборка"}

	if serviceID == "" || serviceID == "new" {
		return h.renderComponent(c, components.ServiceForm(nil, categories, false))
	}

	id, err := uuid.Parse(serviceID)
	if err != nil {
		return h.renderError(c, "Неверный ID услуги")
	}

	service, err := h.getService(c.Context(), id)
	if err != nil {
		return h.renderError(c, "Услуга не найдена")
	}

	return h.renderComponent(c, components.ServiceForm(&service, categories, true))
}

// HTMXCreateService создает новую услугу
func (h *Handlers) HTMXCreateService(c *fiber.Ctx) error {
	name := c.FormValue("name")
	description := c.FormValue("description")
	priceStr := c.FormValue("price")
	durationStr := c.FormValue("duration")
	category := c.FormValue("category")
	isActiveStr := c.FormValue("is_active")

	if name == "" || priceStr == "" {
		return h.renderError(c, "Название и цена обязательны")
	}

	price, err := strconv.ParseFloat(priceStr, 64)
	if err != nil {
		return h.renderError(c, "Неверная цена")
	}

	var duration *int32
	if durationStr != "" {
		d, err := strconv.ParseInt(durationStr, 10, 32)
		if err != nil {
			return h.renderError(c, "Неверная длительность")
		}
		d32 := int32(d)
		duration = &d32
	}

	isActive := isActiveStr == "true"

	service, err := h.createService(c.Context(), name, description, price, duration, category, isActive)
	if err != nil {
		return h.renderError(c, "Ошибка создания услуги")
	}

	return h.renderComponent(c, components.ServiceCard(service))
}

// HTMXNotification возвращает уведомление
func (h *Handlers) HTMXNotification(c *fiber.Ctx) error {
	message := c.Query("message", "Операция выполнена")
	notificationType := c.Query("type", "success")

	return h.renderComponent(c, components.NotificationToast(message, notificationType))
}

// Вспомогательные методы для работы с базой данных
func (h *Handlers) getFilteredTasks(ctx context.Context, status, priority, search string) ([]queries.Task, error) {
	// Здесь должна быть логика фильтрации задач
	// Пока возвращаем все задачи
	return []queries.Task{}, nil
}

func (h *Handlers) getClients(ctx context.Context) ([]queries.Client, error) {
	// Здесь должна быть логика получения клиентов
	return []queries.Client{}, nil
}

func (h *Handlers) getTask(ctx context.Context, id uuid.UUID) (queries.Task, error) {
	// Здесь должна быть логика получения задачи
	return queries.Task{}, nil
}

func (h *Handlers) createTask(ctx context.Context, title, description string, clientID uuid.UUID, priority, estimatedCost, dueDate string) (queries.Task, error) {
	// Здесь должна быть логика создания задачи
	return queries.Task{}, nil
}

func (h *Handlers) updateTaskStatus(ctx context.Context, id uuid.UUID, action string) (queries.Task, error) {
	// Здесь должна быть логика обновления статуса
	return queries.Task{}, nil
}

func (h *Handlers) getFilteredClients(ctx context.Context, search string) ([]queries.Client, error) {
	// Здесь должна быть логика фильтрации клиентов
	return []queries.Client{}, nil
}

func (h *Handlers) getClient(ctx context.Context, id uuid.UUID) (queries.Client, error) {
	// Здесь должна быть логика получения клиента
	return queries.Client{}, nil
}

func (h *Handlers) createClient(ctx context.Context, name, phone, email, address, notes string) (queries.Client, error) {
	// Здесь должна быть логика создания клиента
	return queries.Client{}, nil
}

func (h *Handlers) getFilteredServices(ctx context.Context, category, status, search string) ([]queries.Service, error) {
	// Здесь должна быть логика фильтрации услуг
	return []queries.Service{}, nil
}

func (h *Handlers) getService(ctx context.Context, id uuid.UUID) (queries.Service, error) {
	// Здесь должна быть логика получения услуги
	return queries.Service{}, nil
}

func (h *Handlers) createService(ctx context.Context, name, description string, price float64, duration *int32, category string, isActive bool) (queries.Service, error) {
	// Здесь должна быть логика создания услуги
	return queries.Service{}, nil
}

// renderComponent рендерит Templ компонент в HTML
func (h *Handlers) renderComponent(c *fiber.Ctx, component interface{}) error {
	c.Set("Content-Type", "text/html")
	// Здесь должна быть логика рендеринга Templ компонента
	// Пока возвращаем заглушку
	return c.SendString("<div>Component rendered</div>")
}

// renderError рендерит ошибку для HTMX
func (h *Handlers) renderError(c *fiber.Ctx, message string) error {
	c.Set("Content-Type", "text/html")
	return c.SendString(`<div class="alert alert-error">` + message + `</div>`)
}
