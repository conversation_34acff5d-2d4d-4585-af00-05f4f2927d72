package handlers

import (
	"velomasters/internal/database/queries"
	"velomasters/internal/services"
	"velomasters/web/templates/components"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// ListParts обрабатывает GET /api/parts
func (h *Handlers) ListParts(c *fiber.Ctx) error {
	parts, err := h.services.Part.ListParts(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list parts")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderPartsHTML(c, parts)
	}

	return c.JSON(parts)
}

// GetPart обрабатывает GET /api/parts/:id
func (h *Handlers) GetPart(c *fiber.Ctx) error {
	idStr := c.<PERSON>ms("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid part ID",
		})
	}

	part, err := h.services.Part.GetPart(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to get part")
	}

	return c.JSON(part)
}

// CreatePart обрабатывает POST /api/parts
func (h *Handlers) CreatePart(c *fiber.Ctx) error {
	var req services.CreatePartRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	part, err := h.services.Part.CreatePart(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create part")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderPartHTML(c, part)
	}

	return c.Status(fiber.StatusCreated).JSON(part)
}

// UpdatePart обрабатывает PUT /api/parts/:id
func (h *Handlers) UpdatePart(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid part ID",
		})
	}

	var req services.UpdatePartRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	part, err := h.services.Part.UpdatePart(c.Context(), id, req)
	if err != nil {
		return h.handleError(c, err, "Failed to update part")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderPartHTML(c, part)
	}

	return c.JSON(part)
}

// UpdatePartStock обрабатывает PATCH /api/parts/:id/stock
func (h *Handlers) UpdatePartStock(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid part ID",
		})
	}

	var req services.UpdatePartStockRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	part, err := h.services.Part.UpdatePartStock(c.Context(), id, req)
	if err != nil {
		return h.handleError(c, err, "Failed to update part stock")
	}

	return c.JSON(part)
}

// DeletePart обрабатывает DELETE /api/parts/:id
func (h *Handlers) DeletePart(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid part ID",
		})
	}

	err = h.services.Part.DeletePart(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to delete part")
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// SearchParts обрабатывает GET /api/parts/search
func (h *Handlers) SearchParts(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Search query is required",
		})
	}

	parts, err := h.services.Part.SearchParts(c.Context(), query)
	if err != nil {
		return h.handleError(c, err, "Failed to search parts")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderSearchPartsHTML(c, parts)
	}

	return c.JSON(parts)
}

// ListPartsByCategory обрабатывает GET /api/parts/category/:id
func (h *Handlers) ListPartsByCategory(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid category ID",
		})
	}

	parts, err := h.services.Part.ListPartsByCategory(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to list parts by category")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderPartsByCategoryHTML(c, parts)
	}

	return c.JSON(parts)
}

// ListLowStockParts обрабатывает GET /api/parts/low-stock
func (h *Handlers) ListLowStockParts(c *fiber.Ctx) error {
	parts, err := h.services.Part.ListLowStockParts(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list low stock parts")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderLowStockPartsHTML(c, parts)
	}

	return c.JSON(parts)
}

// ListPartCategories обрабатывает GET /api/part-categories
func (h *Handlers) ListPartCategories(c *fiber.Ctx) error {
	categories, err := h.services.Part.ListPartCategories(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list part categories")
	}

	return c.JSON(categories)
}

// CreatePartCategory обрабатывает POST /api/part-categories
func (h *Handlers) CreatePartCategory(c *fiber.Ctx) error {
	var req services.CreatePartCategoryRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	category, err := h.services.Part.CreatePartCategory(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create part category")
	}

	return c.Status(fiber.StatusCreated).JSON(category)
}

// ListSuppliers обрабатывает GET /api/suppliers
func (h *Handlers) ListSuppliers(c *fiber.Ctx) error {
	suppliers, err := h.services.Part.ListSuppliers(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list suppliers")
	}

	return c.JSON(suppliers)
}

// CreateSupplier обрабатывает POST /api/suppliers
func (h *Handlers) CreateSupplier(c *fiber.Ctx) error {
	var req services.CreateSupplierRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	supplier, err := h.services.Part.CreateSupplier(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create supplier")
	}

	return c.Status(fiber.StatusCreated).JSON(supplier)
}

// SearchSuppliers обрабатывает GET /api/suppliers/search
func (h *Handlers) SearchSuppliers(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Search query is required",
		})
	}

	suppliers, err := h.services.Part.SearchSuppliers(c.Context(), query)
	if err != nil {
		return h.handleError(c, err, "Failed to search suppliers")
	}

	return c.JSON(suppliers)
}

// HTMX обработчики для компонентов

// HTMXListParts обрабатывает HTMX запросы для списка запчастей
func (h *Handlers) HTMXListParts(c *fiber.Ctx) error {
	parts, err := h.services.Part.ListParts(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list parts")
	}

	return h.renderPartsHTML(c, parts)
}

// HTMXPartForm обрабатывает HTMX запросы для формы запчасти
func (h *Handlers) HTMXPartForm(c *fiber.Ctx) error {
	idStr := c.Params("id")
	var part *queries.GetPartRow
	var err error

	if idStr != "" && idStr != "new" {
		id, parseErr := uuid.Parse(idStr)
		if parseErr != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid part ID",
			})
		}
		part, err = h.services.Part.GetPart(c.Context(), id)
		if err != nil {
			return h.handleError(c, err, "Failed to get part")
		}
	}

	// Получаем категории и поставщиков для формы
	categories, err := h.services.Part.ListPartCategories(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list categories")
	}

	suppliers, err := h.services.Part.ListSuppliers(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list suppliers")
	}

	return renderTempl(c, components.PartForm(part, categories, suppliers, part != nil))
}

// HTMXCreatePart обрабатывает HTMX создание запчасти
func (h *Handlers) HTMXCreatePart(c *fiber.Ctx) error {
	var req services.CreatePartRequest
	if err := c.BodyParser(&req); err != nil {
		return h.renderError(c, "Invalid form data")
	}

	part, err := h.services.Part.CreatePart(c.Context(), req)
	if err != nil {
		return h.renderError(c, "Failed to create part")
	}

	return h.renderPartHTML(c, part)
}

// Вспомогательные методы для рендеринга HTML

// renderPartsHTML рендерит список запчастей в HTML
func (h *Handlers) renderPartsHTML(c *fiber.Ctx, parts []queries.ListPartsRow) error {
	return renderTempl(c, components.PartList(parts, len(parts) == 0))
}

// renderPartHTML рендерит одну запчасть в HTML
func (h *Handlers) renderPartHTML(c *fiber.Ctx, part *queries.Part) error {
	// Конвертируем Part в ListPartsRow для отображения
	partRow := queries.ListPartsRow{
		ID:              part.ID,
		CategoryID:      part.CategoryID,
		SupplierID:      part.SupplierID,
		Name:            part.Name,
		Description:     part.Description,
		PartNumber:      part.PartNumber,
		Brand:           part.Brand,
		Price:           part.Price,
		Cost:            part.Cost,
		QuantityInStock: part.QuantityInStock,
		MinStockLevel:   part.MinStockLevel,
		IsActive:        part.IsActive,
		CreatedAt:       part.CreatedAt,
		UpdatedAt:       part.UpdatedAt,
	}
	return renderTempl(c, components.PartCard(partRow))
}

// renderSearchPartsHTML рендерит результаты поиска запчастей
func (h *Handlers) renderSearchPartsHTML(c *fiber.Ctx, parts []queries.SearchPartsRow) error {
	// Конвертируем SearchPartsRow в ListPartsRow
	listParts := make([]queries.ListPartsRow, len(parts))
	for i, part := range parts {
		listParts[i] = queries.ListPartsRow{
			ID:              part.ID,
			CategoryID:      part.CategoryID,
			SupplierID:      part.SupplierID,
			Name:            part.Name,
			Description:     part.Description,
			PartNumber:      part.PartNumber,
			Brand:           part.Brand,
			Price:           part.Price,
			Cost:            part.Cost,
			QuantityInStock: part.QuantityInStock,
			MinStockLevel:   part.MinStockLevel,
			IsActive:        part.IsActive,
			CreatedAt:       part.CreatedAt,
			UpdatedAt:       part.UpdatedAt,
			CategoryName:    part.CategoryName,
			SupplierName:    part.SupplierName,
		}
	}
	return renderTempl(c, components.PartList(listParts, len(listParts) == 0))
}

// renderPartsByCategoryHTML рендерит запчасти по категории
func (h *Handlers) renderPartsByCategoryHTML(c *fiber.Ctx, parts []queries.ListPartsByCategoryRow) error {
	// Конвертируем ListPartsByCategoryRow в ListPartsRow
	listParts := make([]queries.ListPartsRow, len(parts))
	for i, part := range parts {
		listParts[i] = queries.ListPartsRow{
			ID:              part.ID,
			CategoryID:      part.CategoryID,
			SupplierID:      part.SupplierID,
			Name:            part.Name,
			Description:     part.Description,
			PartNumber:      part.PartNumber,
			Brand:           part.Brand,
			Price:           part.Price,
			Cost:            part.Cost,
			QuantityInStock: part.QuantityInStock,
			MinStockLevel:   part.MinStockLevel,
			IsActive:        part.IsActive,
			CreatedAt:       part.CreatedAt,
			UpdatedAt:       part.UpdatedAt,
			CategoryName:    part.CategoryName,
			SupplierName:    part.SupplierName,
		}
	}
	return renderTempl(c, components.PartList(listParts, len(listParts) == 0))
}

// renderLowStockPartsHTML рендерит запчасти с низким остатком
func (h *Handlers) renderLowStockPartsHTML(c *fiber.Ctx, parts []queries.ListLowStockPartsRow) error {
	// Конвертируем ListLowStockPartsRow в ListPartsRow
	listParts := make([]queries.ListPartsRow, len(parts))
	for i, part := range parts {
		listParts[i] = queries.ListPartsRow{
			ID:              part.ID,
			CategoryID:      part.CategoryID,
			SupplierID:      part.SupplierID,
			Name:            part.Name,
			Description:     part.Description,
			PartNumber:      part.PartNumber,
			Brand:           part.Brand,
			Price:           part.Price,
			Cost:            part.Cost,
			QuantityInStock: part.QuantityInStock,
			MinStockLevel:   part.MinStockLevel,
			IsActive:        part.IsActive,
			CreatedAt:       part.CreatedAt,
			UpdatedAt:       part.UpdatedAt,
			CategoryName:    part.CategoryName,
			SupplierName:    part.SupplierName,
		}
	}
	return renderTempl(c, components.PartList(listParts, len(listParts) == 0))
}
