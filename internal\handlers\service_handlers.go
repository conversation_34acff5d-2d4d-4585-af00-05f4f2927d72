package handlers

import (
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"velomasters/internal/database/queries"
	"velomasters/internal/models"
	"velomasters/internal/services"
)

// ListServices обрабатывает GET /api/services
func (h *Handlers) ListServices(c *fiber.Ctx) error {
	servicesList, err := h.services.Service.ListServices(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list services")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderServicesHTML(c, servicesList)
	}

	return c.JSON(servicesList)
}

// GetService обрабатывает GET /api/services/:id
func (h *Handlers) GetService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid service ID",
		})
	}

	service, err := h.services.Service.GetService(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to get service")
	}

	return c.JSON(service)
}

// CreateService обрабатывает POST /api/services
func (h *Handlers) CreateService(c *fiber.Ctx) error {
	var req services.CreateServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	service, err := h.services.Service.CreateService(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create service")
	}

	return c.Status(fiber.StatusCreated).JSON(service)
}

// UpdateService обрабатывает PUT /api/services/:id
func (h *Handlers) UpdateService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid service ID",
		})
	}

	var req services.UpdateServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	service, err := h.services.Service.UpdateService(c.Context(), id, req)
	if err != nil {
		return h.handleError(c, err, "Failed to update service")
	}

	return c.JSON(service)
}

// DeleteService обрабатывает DELETE /api/services/:id
func (h *Handlers) DeleteService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid service ID",
		})
	}

	err = h.services.Service.DeleteService(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to delete service")
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// SearchServices обрабатывает GET /api/services/search?q=query
func (h *Handlers) SearchServices(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Search query is required",
		})
	}

	servicesList, err := h.services.Service.SearchServices(c.Context(), query)
	if err != nil {
		return h.handleError(c, err, "Failed to search services")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderSearchServicesHTML(c, servicesList)
	}

	return c.JSON(servicesList)
}

// ListServiceCategories обрабатывает GET /api/service-categories
func (h *Handlers) ListServiceCategories(c *fiber.Ctx) error {
	categories, err := h.services.Service.ListServiceCategories(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list service categories")
	}

	return c.JSON(categories)
}

// CreateServiceCategory обрабатывает POST /api/service-categories
func (h *Handlers) CreateServiceCategory(c *fiber.Ctx) error {
	var req services.CreateServiceCategoryRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	category, err := h.services.Service.CreateServiceCategory(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create service category")
	}

	return c.Status(fiber.StatusCreated).JSON(category)
}

// renderServicesHTML рендерит HTML для списка услуг
func (h *Handlers) renderServicesHTML(c *fiber.Ctx, servicesList []queries.ListServicesRow) error {
	html := `<div class="space-y-4">`
	
	if len(servicesList) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Услуги не найдены</p>`
	} else {
		for _, service := range servicesList {
			serviceID := models.ConvertPgTypeUUID(service.ID).String()
			categoryName := ""
			if service.CategoryName.Valid {
				categoryName = service.CategoryName.String
			}
			description := ""
			if service.Description.Valid {
				description = service.Description.String
			}
			
			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<div class="flex justify-between items-start">
						<div class="flex-1">
							<h3 class="text-lg font-semibold">` + service.Name + `</h3>
							<p class="text-sm text-gray-600 mb-2">` + description + `</p>
							<div class="flex space-x-4 text-sm">
								<span class="text-gray-600">Категория: ` + categoryName + `</span>
								<span class="text-green-600 font-medium">` + formatFloat(models.ConvertPgTypeNumeric(service.Price)) + ` ₽</span>
								<span class="text-gray-600">` + formatInt(service.DurationMinutes.Int32) + ` мин</span>
							</div>
						</div>
						<div class="flex space-x-2">
							<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
									hx-get="/api/services/` + serviceID + `"
									hx-target="#service-details">
								Просмотр
							</button>
							<button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
									hx-delete="/api/services/` + serviceID + `"
									hx-confirm="Вы уверены, что хотите удалить эту услугу?"
									hx-target="closest .border">
								Удалить
							</button>
						</div>
					</div>
				</div>
			`
		}
	}
	
	html += `</div>`
	
	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}

// renderSearchServicesHTML рендерит HTML для результатов поиска услуг
func (h *Handlers) renderSearchServicesHTML(c *fiber.Ctx, servicesList []queries.SearchServicesRow) error {
	html := `<div class="space-y-4">`
	
	if len(servicesList) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Услуги не найдены</p>`
	} else {
		for _, service := range servicesList {
			serviceID := models.ConvertPgTypeUUID(service.ID).String()
			categoryName := ""
			if service.CategoryName.Valid {
				categoryName = service.CategoryName.String
			}
			
			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<div class="flex justify-between items-start">
						<div class="flex-1">
							<h3 class="text-lg font-semibold">` + service.Name + `</h3>
							<div class="flex space-x-4 text-sm">
								<span class="text-gray-600">Категория: ` + categoryName + `</span>
								<span class="text-green-600 font-medium">` + formatFloat(models.ConvertPgTypeNumeric(service.Price)) + ` ₽</span>
							</div>
						</div>
						<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
								hx-get="/api/services/` + serviceID + `"
								hx-target="#service-details">
							Просмотр
						</button>
					</div>
				</div>
			`
		}
	}
	
	html += `</div>`
	
	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}
