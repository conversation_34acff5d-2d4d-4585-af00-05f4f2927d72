package handlers

import (
	"velomasters/internal/database/queries"
	"velomasters/internal/models"
	"velomasters/internal/services"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// ListTasks обрабатывает GET /api/tasks
func (h *Handlers) ListTasks(c *fiber.Ctx) error {
	tasks, err := h.services.Task.ListTasks(c.Context())
	if err != nil {
		return h.handleError(c, err, "Failed to list tasks")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderTasksHTML(c, tasks)
	}

	return c.JSON(tasks)
}

// GetTask обрабатывает GET /api/tasks/:id
func (h *Handlers) GetTask(c *fiber.Ctx) error {
	idStr := c.<PERSON>ms("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid task ID",
		})
	}

	task, err := h.services.Task.GetTask(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to get task")
	}

	return c.JSON(task)
}

// CreateTask обрабатывает POST /api/tasks
func (h *Handlers) CreateTask(c *fiber.Ctx) error {
	var req services.CreateTaskRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	task, err := h.services.Task.CreateTask(c.Context(), req)
	if err != nil {
		return h.handleError(c, err, "Failed to create task")
	}

	return c.Status(fiber.StatusCreated).JSON(task)
}

// UpdateTask обрабатывает PUT /api/tasks/:id
func (h *Handlers) UpdateTask(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid task ID",
		})
	}

	var req services.UpdateTaskRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	task, err := h.services.Task.UpdateTask(c.Context(), id, req)
	if err != nil {
		return h.handleError(c, err, "Failed to update task")
	}

	return c.JSON(task)
}

// DeleteTask обрабатывает DELETE /api/tasks/:id
func (h *Handlers) DeleteTask(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid task ID",
		})
	}

	err = h.services.Task.DeleteTask(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to delete task")
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// SearchTasks обрабатывает GET /api/tasks/search?q=query
func (h *Handlers) SearchTasks(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Search query is required",
		})
	}

	tasks, err := h.services.Task.SearchTasks(c.Context(), query)
	if err != nil {
		return h.handleError(c, err, "Failed to search tasks")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderSearchTasksHTML(c, tasks)
	}

	return c.JSON(tasks)
}

// StartTask обрабатывает POST /api/tasks/:id/start
func (h *Handlers) StartTask(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid task ID",
		})
	}

	task, err := h.services.Task.StartTask(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to start task")
	}

	return c.JSON(task)
}

// CompleteTask обрабатывает POST /api/tasks/:id/complete
func (h *Handlers) CompleteTask(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid task ID",
		})
	}

	task, err := h.services.Task.CompleteTask(c.Context(), id)
	if err != nil {
		return h.handleError(c, err, "Failed to complete task")
	}

	return c.JSON(task)
}

// ListTasksByStatus обрабатывает GET /api/tasks/status/:status
func (h *Handlers) ListTasksByStatus(c *fiber.Ctx) error {
	status := c.Params("status")

	tasks, err := h.services.Task.ListTasksByStatus(c.Context(), status)
	if err != nil {
		return h.handleError(c, err, "Failed to list tasks by status")
	}

	// Проверяем, нужен ли HTML ответ для HTMX
	if c.Get("HX-Request") == "true" {
		return h.renderTasksByStatusHTML(c, tasks)
	}

	return c.JSON(tasks)
}

// renderTasksHTML рендерит HTML для списка задач
func (h *Handlers) renderTasksHTML(c *fiber.Ctx, tasks []queries.ListTasksRow) error {
	html := `<div class="space-y-4">`

	if len(tasks) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Задачи не найдены</p>`
	} else {
		for _, task := range tasks {
			taskID := models.ConvertPgTypeUUID(task.ID).String()
			status := getStatusDisplay(string(task.Status.TaskStatus))
			priority := getPriorityDisplay(string(task.Priority.TaskPriority))

			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<div class="flex justify-between items-start">
						<div class="flex-1">
							<h3 class="text-lg font-semibold">` + task.Title + `</h3>
							<p class="text-sm text-gray-600 mb-2">Клиент: ` + task.ClientName + `</p>
							<div class="flex space-x-4 text-sm">
								<span class="px-2 py-1 rounded text-white bg-` + status.Color + `-500">` + status.Label + `</span>
								<span class="px-2 py-1 rounded text-white bg-` + priority.Color + `-500">` + priority.Label + `</span>
								<span class="text-gray-600">Стоимость: ` + formatFloat(models.ConvertPgTypeNumeric(task.EstimatedCost)) + ` ₽</span>
							</div>
						</div>
						<div class="flex space-x-2">
							<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
									hx-get="/api/tasks/` + taskID + `"
									hx-target="#task-details">
								Просмотр
							</button>
							<button class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
									hx-post="/api/tasks/` + taskID + `/start"
									hx-target="closest .border">
								Запустить
							</button>
							<button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
									hx-delete="/api/tasks/` + taskID + `"
									hx-confirm="Вы уверены, что хотите удалить эту задачу?"
									hx-target="closest .border">
								Удалить
							</button>
						</div>
					</div>
				</div>
			`
		}
	}

	html += `</div>`

	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}

// renderSearchTasksHTML рендерит HTML для результатов поиска задач
func (h *Handlers) renderSearchTasksHTML(c *fiber.Ctx, tasks []queries.SearchTasksRow) error {
	html := `<div class="space-y-4">`

	if len(tasks) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Задачи не найдены</p>`
	} else {
		for _, task := range tasks {
			taskID := models.ConvertPgTypeUUID(task.ID).String()

			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<h3 class="text-lg font-semibold">` + task.Title + `</h3>
					<p class="text-sm text-gray-600">Клиент: ` + task.ClientName + `</p>
					<div class="flex justify-between items-center mt-2">
						<span class="text-sm text-gray-500">ID: ` + taskID + `</span>
						<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
								hx-get="/api/tasks/` + taskID + `"
								hx-target="#task-details">
							Просмотр
						</button>
					</div>
				</div>
			`
		}
	}

	html += `</div>`

	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}

// renderTasksByStatusHTML рендерит HTML для задач по статусу
func (h *Handlers) renderTasksByStatusHTML(c *fiber.Ctx, tasks []queries.ListTasksByStatusRow) error {
	html := `<div class="space-y-4">`

	if len(tasks) == 0 {
		html += `<p class="text-gray-500 text-center py-8">Задачи не найдены</p>`
	} else {
		for _, task := range tasks {
			taskID := models.ConvertPgTypeUUID(task.ID).String()

			html += `
				<div class="border rounded-lg p-4 hover:bg-gray-50">
					<h3 class="text-lg font-semibold">` + task.Title + `</h3>
					<p class="text-sm text-gray-600">Клиент: ` + task.ClientName + `</p>
					<div class="flex justify-between items-center mt-2">
						<span class="text-sm text-gray-500">Стоимость: ` + formatFloat(models.ConvertPgTypeNumeric(task.EstimatedCost)) + ` ₽</span>
						<button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
								hx-get="/api/tasks/` + taskID + `"
								hx-target="#task-details">
							Просмотр
						</button>
					</div>
				</div>
			`
		}
	}

	html += `</div>`

	c.Set("Content-Type", "text/html")
	return c.SendString(html)
}
