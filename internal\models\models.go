package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

// TaskStatusDisplay представляет статус задачи для отображения
type TaskStatusDisplay struct {
	Value string
	Label string
	Color string
}

// TaskPriorityDisplay представляет приоритет задачи для отображения
type TaskPriorityDisplay struct {
	Value string
	Label string
	Color string
}

// GetTaskStatuses возвращает все возможные статусы задач
func GetTaskStatuses() []TaskStatusDisplay {
	return []TaskStatusDisplay{
		{Value: "pending", Label: "Ожидает", Color: "gray"},
		{Value: "in_progress", Label: "В работе", Color: "blue"},
		{Value: "waiting_parts", Label: "Ожидает запчасти", Color: "yellow"},
		{Value: "waiting_client", Label: "Ожидает клиента", Color: "orange"},
		{Value: "completed", Label: "Завершена", Color: "green"},
		{Value: "cancelled", Label: "Отменена", Color: "red"},
	}
}

// GetTaskPriorities возвращает все возможные приоритеты задач
func GetTaskPriorities() []TaskPriorityDisplay {
	return []TaskPriorityDisplay{
		{Value: "low", Label: "Низкий", Color: "gray"},
		{Value: "normal", Label: "Обычный", Color: "blue"},
		{Value: "high", Label: "Высокий", Color: "orange"},
		{Value: "urgent", Label: "Срочный", Color: "red"},
	}
}

// TaskWithDetails представляет задачу с дополнительной информацией
type TaskWithDetails struct {
	ID                  uuid.UUID  `json:"id"`
	ClientID            uuid.UUID  `json:"client_id"`
	ClientName          string     `json:"client_name"`
	BicycleID           *uuid.UUID `json:"bicycle_id,omitempty"`
	BicycleBrand        *string    `json:"bicycle_brand,omitempty"`
	BicycleModel        *string    `json:"bicycle_model,omitempty"`
	Title               string     `json:"title"`
	Description         *string    `json:"description,omitempty"`
	Status              string     `json:"status"`
	Priority            string     `json:"priority"`
	EstimatedCost       float64    `json:"estimated_cost"`
	ActualCost          float64    `json:"actual_cost"`
	EstimatedCompletion *time.Time `json:"estimated_completion,omitempty"`
	StartedAt           *time.Time `json:"started_at,omitempty"`
	CompletedAt         *time.Time `json:"completed_at,omitempty"`
	Notes               *string    `json:"notes,omitempty"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
}

// ServiceWithCategory представляет услугу с категорией
type ServiceWithCategory struct {
	ID              uuid.UUID `json:"id"`
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	CategoryName    *string    `json:"category_name,omitempty"`
	Name            string     `json:"name"`
	Description     *string    `json:"description,omitempty"`
	Price           float64    `json:"price"`
	DurationMinutes int32      `json:"duration_minutes"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// PartWithDetails представляет запчасть с дополнительной информацией
type PartWithDetails struct {
	ID              uuid.UUID `json:"id"`
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	CategoryName    *string    `json:"category_name,omitempty"`
	SupplierID      *uuid.UUID `json:"supplier_id,omitempty"`
	SupplierName    *string    `json:"supplier_name,omitempty"`
	Name            string     `json:"name"`
	Description     *string    `json:"description,omitempty"`
	PartNumber      *string    `json:"part_number,omitempty"`
	Brand           *string    `json:"brand,omitempty"`
	Price           float64    `json:"price"`
	Cost            float64    `json:"cost"`
	QuantityInStock int32      `json:"quantity_in_stock"`
	MinStockLevel   int32      `json:"min_stock_level"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// BicycleWithClient представляет велосипед с информацией о клиенте
type BicycleWithClient struct {
	ID          uuid.UUID `json:"id"`
	ClientID    uuid.UUID `json:"client_id"`
	ClientName  string    `json:"client_name"`
	Brand       *string   `json:"brand,omitempty"`
	Model       *string   `json:"model,omitempty"`
	Year        *int32    `json:"year,omitempty"`
	Color       *string   `json:"color,omitempty"`
	FrameNumber *string   `json:"frame_number,omitempty"`
	Description *string   `json:"description,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TaskServiceWithDetails представляет связь задачи и услуги с деталями
type TaskServiceWithDetails struct {
	ID                 uuid.UUID `json:"id"`
	TaskID             uuid.UUID `json:"task_id"`
	ServiceID          uuid.UUID `json:"service_id"`
	ServiceName        string    `json:"service_name"`
	ServiceDescription *string   `json:"service_description,omitempty"`
	Quantity           int32     `json:"quantity"`
	Price              float64   `json:"price"`
	Notes              *string   `json:"notes,omitempty"`
	CreatedAt          time.Time `json:"created_at"`
}

// TaskPartWithDetails представляет связь задачи и запчасти с деталями
type TaskPartWithDetails struct {
	ID              uuid.UUID `json:"id"`
	TaskID          uuid.UUID `json:"task_id"`
	PartID          uuid.UUID `json:"part_id"`
	PartName        string    `json:"part_name"`
	PartDescription *string   `json:"part_description,omitempty"`
	PartNumber      *string   `json:"part_number,omitempty"`
	Quantity        int32     `json:"quantity"`
	Price           float64   `json:"price"`
	Notes           *string   `json:"notes,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
}

// ConvertPgTypeUUID конвертирует pgtype.UUID в uuid.UUID
func ConvertPgTypeUUID(pgUUID pgtype.UUID) uuid.UUID {
	if pgUUID.Valid {
		return pgUUID.Bytes
	}
	return uuid.Nil
}

// ConvertUUIDToPgType конвертирует uuid.UUID в pgtype.UUID
func ConvertUUIDToPgType(id uuid.UUID) pgtype.UUID {
	return pgtype.UUID{
		Bytes: id,
		Valid: true,
	}
}

// ConvertPgTypeText конвертирует pgtype.Text в *string
func ConvertPgTypeText(pgText pgtype.Text) *string {
	if pgText.Valid {
		return &pgText.String
	}
	return nil
}

// ConvertStringToPgType конвертирует *string в pgtype.Text
func ConvertStringToPgType(s *string) pgtype.Text {
	if s != nil {
		return pgtype.Text{
			String: *s,
			Valid:  true,
		}
	}
	return pgtype.Text{Valid: false}
}

// ConvertPgTypeNumeric конвертирует pgtype.Numeric в float64
func ConvertPgTypeNumeric(pgNum pgtype.Numeric) float64 {
	if pgNum.Valid {
		f, _ := pgNum.Float64Value()
		return f.Float64
	}
	return 0
}

// ConvertFloat64ToPgType конвертирует float64 в pgtype.Numeric
func ConvertFloat64ToPgType(f float64) pgtype.Numeric {
	var pgNum pgtype.Numeric
	pgNum.Scan(f)
	return pgNum
}
