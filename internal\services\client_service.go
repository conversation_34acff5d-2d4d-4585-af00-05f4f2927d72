package services

import (
	"context"
	"fmt"

	"velomasters/internal/database/queries"
	"velomasters/internal/models"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

// ClientService предоставляет методы для работы с клиентами
type ClientService struct {
	db      *pgxpool.Pool
	queries *queries.Queries
}

// NewClientService создает новый экземпляр ClientService
func NewClientService(db *pgxpool.Pool) *ClientService {
	return &ClientService{
		db:      db,
		queries: queries.New(db),
	}
}

// CreateClientRequest представляет запрос на создание клиента
type CreateClientRequest struct {
	Name    string  `json:"name" validate:"required"`
	Phone   *string `json:"phone,omitempty"`
	Email   *string `json:"email,omitempty"`
	Address *string `json:"address,omitempty"`
	Notes   *string `json:"notes,omitempty"`
}

// UpdateClientRequest представляет запрос на обновление клиента
type UpdateClientRequest struct {
	Name    string  `json:"name" validate:"required"`
	Phone   *string `json:"phone,omitempty"`
	Email   *string `json:"email,omitempty"`
	Address *string `json:"address,omitempty"`
	Notes   *string `json:"notes,omitempty"`
}

// CreateClient создает нового клиента
func (s *ClientService) CreateClient(ctx context.Context, req CreateClientRequest) (*queries.Client, error) {
	client, err := s.queries.CreateClient(ctx, queries.CreateClientParams{
		Name:    req.Name,
		Phone:   models.ConvertStringToPgType(req.Phone),
		Email:   models.ConvertStringToPgType(req.Email),
		Address: models.ConvertStringToPgType(req.Address),
		Notes:   models.ConvertStringToPgType(req.Notes),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create client: %w", err)
	}

	return &client, nil
}

// GetClient получает клиента по ID
func (s *ClientService) GetClient(ctx context.Context, id uuid.UUID) (*queries.Client, error) {
	client, err := s.queries.GetClient(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	return &client, nil
}

// GetClientByPhone получает клиента по номеру телефона
func (s *ClientService) GetClientByPhone(ctx context.Context, phone string) (*queries.Client, error) {
	client, err := s.queries.GetClientByPhone(ctx, models.ConvertStringToPgType(&phone))
	if err != nil {
		return nil, fmt.Errorf("failed to get client by phone: %w", err)
	}

	return &client, nil
}

// ListClients получает список всех клиентов
func (s *ClientService) ListClients(ctx context.Context) ([]queries.Client, error) {
	clients, err := s.queries.ListClients(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list clients: %w", err)
	}

	return clients, nil
}

// SearchClients ищет клиентов по запросу
func (s *ClientService) SearchClients(ctx context.Context, query string) ([]queries.Client, error) {
	clients, err := s.queries.SearchClients(ctx, pgtype.Text{String: query, Valid: true})
	if err != nil {
		return nil, fmt.Errorf("failed to search clients: %w", err)
	}

	return clients, nil
}

// UpdateClient обновляет информацию о клиенте
func (s *ClientService) UpdateClient(ctx context.Context, id uuid.UUID, req UpdateClientRequest) (*queries.Client, error) {
	client, err := s.queries.UpdateClient(ctx, queries.UpdateClientParams{
		ID:      models.ConvertUUIDToPgType(id),
		Name:    req.Name,
		Phone:   models.ConvertStringToPgType(req.Phone),
		Email:   models.ConvertStringToPgType(req.Email),
		Address: models.ConvertStringToPgType(req.Address),
		Notes:   models.ConvertStringToPgType(req.Notes),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update client: %w", err)
	}

	return &client, nil
}

// DeleteClient удаляет клиента
func (s *ClientService) DeleteClient(ctx context.Context, id uuid.UUID) error {
	err := s.queries.DeleteClient(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return fmt.Errorf("failed to delete client: %w", err)
	}

	return nil
}
