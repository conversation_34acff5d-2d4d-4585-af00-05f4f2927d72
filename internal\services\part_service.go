package services

import (
	"context"
	"fmt"

	"velomasters/internal/database/queries"
	"velomasters/internal/models"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

// PartService предоставляет методы для работы с запчастями
type PartService struct {
	db      *pgxpool.Pool
	queries *queries.Queries
}

// NewPartService создает новый экземпляр PartService
func NewPartService(db *pgxpool.Pool) *PartService {
	return &PartService{
		db:      db,
		queries: queries.New(db),
	}
}

// CreatePartCategoryRequest представляет запрос на создание категории запчастей
type CreatePartCategoryRequest struct {
	Name        string  `json:"name" validate:"required"`
	Description *string `json:"description,omitempty"`
}

// CreateSupplierRequest представляет запрос на создание поставщика
type CreateSupplierRequest struct {
	Name          string  `json:"name" validate:"required"`
	ContactPerson *string `json:"contact_person,omitempty"`
	Phone         *string `json:"phone,omitempty"`
	Email         *string `json:"email,omitempty"`
	Address       *string `json:"address,omitempty"`
	Website       *string `json:"website,omitempty"`
	Notes         *string `json:"notes,omitempty"`
}

// CreatePartRequest представляет запрос на создание запчасти
type CreatePartRequest struct {
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	SupplierID      *uuid.UUID `json:"supplier_id,omitempty"`
	Name            string     `json:"name" validate:"required"`
	Description     *string    `json:"description,omitempty"`
	PartNumber      *string    `json:"part_number,omitempty"`
	Brand           *string    `json:"brand,omitempty"`
	Price           float64    `json:"price"`
	Cost            float64    `json:"cost"`
	QuantityInStock int32      `json:"quantity_in_stock"`
	MinStockLevel   int32      `json:"min_stock_level"`
	IsActive        bool       `json:"is_active"`
}

// UpdatePartRequest представляет запрос на обновление запчасти
type UpdatePartRequest struct {
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	SupplierID      *uuid.UUID `json:"supplier_id,omitempty"`
	Name            string     `json:"name" validate:"required"`
	Description     *string    `json:"description,omitempty"`
	PartNumber      *string    `json:"part_number,omitempty"`
	Brand           *string    `json:"brand,omitempty"`
	Price           float64    `json:"price"`
	Cost            float64    `json:"cost"`
	QuantityInStock int32      `json:"quantity_in_stock"`
	MinStockLevel   int32      `json:"min_stock_level"`
	IsActive        bool       `json:"is_active"`
}

// UpdatePartStockRequest представляет запрос на обновление остатков запчасти
type UpdatePartStockRequest struct {
	QuantityInStock int32 `json:"quantity_in_stock"`
}

// CreatePartCategory создает новую категорию запчастей
func (s *PartService) CreatePartCategory(ctx context.Context, req CreatePartCategoryRequest) (*queries.PartCategory, error) {
	category, err := s.queries.CreatePartCategory(ctx, queries.CreatePartCategoryParams{
		Name:        req.Name,
		Description: models.ConvertStringToPgType(req.Description),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create part category: %w", err)
	}

	return &category, nil
}

// CreateSupplier создает нового поставщика
func (s *PartService) CreateSupplier(ctx context.Context, req CreateSupplierRequest) (*queries.Supplier, error) {
	supplier, err := s.queries.CreateSupplier(ctx, queries.CreateSupplierParams{
		Name:          req.Name,
		ContactPerson: models.ConvertStringToPgType(req.ContactPerson),
		Phone:         models.ConvertStringToPgType(req.Phone),
		Email:         models.ConvertStringToPgType(req.Email),
		Address:       models.ConvertStringToPgType(req.Address),
		Website:       models.ConvertStringToPgType(req.Website),
		Notes:         models.ConvertStringToPgType(req.Notes),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create supplier: %w", err)
	}

	return &supplier, nil
}

// CreatePart создает новую запчасть
func (s *PartService) CreatePart(ctx context.Context, req CreatePartRequest) (*queries.Part, error) {
	var categoryID, supplierID pgtype.UUID
	if req.CategoryID != nil {
		categoryID = models.ConvertUUIDToPgType(*req.CategoryID)
	}
	if req.SupplierID != nil {
		supplierID = models.ConvertUUIDToPgType(*req.SupplierID)
	}

	part, err := s.queries.CreatePart(ctx, queries.CreatePartParams{
		CategoryID:      categoryID,
		SupplierID:      supplierID,
		Name:            req.Name,
		Description:     models.ConvertStringToPgType(req.Description),
		PartNumber:      models.ConvertStringToPgType(req.PartNumber),
		Brand:           models.ConvertStringToPgType(req.Brand),
		Price:           models.ConvertFloat64ToPgType(req.Price),
		Cost:            models.ConvertFloat64ToPgType(req.Cost),
		QuantityInStock: pgtype.Int4{Int32: req.QuantityInStock, Valid: true},
		MinStockLevel:   pgtype.Int4{Int32: req.MinStockLevel, Valid: true},
		IsActive:        pgtype.Bool{Bool: req.IsActive, Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create part: %w", err)
	}

	return &part, nil
}

// GetPart получает запчасть по ID
func (s *PartService) GetPart(ctx context.Context, id uuid.UUID) (*queries.GetPartRow, error) {
	part, err := s.queries.GetPart(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get part: %w", err)
	}

	return &part, nil
}

// ListParts получает список всех активных запчастей
func (s *PartService) ListParts(ctx context.Context) ([]queries.ListPartsRow, error) {
	parts, err := s.queries.ListParts(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list parts: %w", err)
	}

	return parts, nil
}

// ListPartsByCategory получает список запчастей по категории
func (s *PartService) ListPartsByCategory(ctx context.Context, categoryID uuid.UUID) ([]queries.ListPartsByCategoryRow, error) {
	parts, err := s.queries.ListPartsByCategory(ctx, models.ConvertUUIDToPgType(categoryID))
	if err != nil {
		return nil, fmt.Errorf("failed to list parts by category: %w", err)
	}

	return parts, nil
}

// ListLowStockParts получает список запчастей с низким остатком
func (s *PartService) ListLowStockParts(ctx context.Context) ([]queries.ListLowStockPartsRow, error) {
	parts, err := s.queries.ListLowStockParts(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list low stock parts: %w", err)
	}

	return parts, nil
}

// SearchParts ищет запчасти по тексту
func (s *PartService) SearchParts(ctx context.Context, query string) ([]queries.SearchPartsRow, error) {
	parts, err := s.queries.SearchParts(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to search parts: %w", err)
	}

	return parts, nil
}

// UpdatePart обновляет запчасть
func (s *PartService) UpdatePart(ctx context.Context, id uuid.UUID, req UpdatePartRequest) (*queries.Part, error) {
	var categoryID, supplierID pgtype.UUID
	if req.CategoryID != nil {
		categoryID = models.ConvertUUIDToPgType(*req.CategoryID)
	}
	if req.SupplierID != nil {
		supplierID = models.ConvertUUIDToPgType(*req.SupplierID)
	}

	part, err := s.queries.UpdatePart(ctx, queries.UpdatePartParams{
		ID:              models.ConvertUUIDToPgType(id),
		CategoryID:      categoryID,
		SupplierID:      supplierID,
		Name:            req.Name,
		Description:     models.ConvertStringToPgType(req.Description),
		PartNumber:      models.ConvertStringToPgType(req.PartNumber),
		Brand:           models.ConvertStringToPgType(req.Brand),
		Price:           models.ConvertFloat64ToPgType(req.Price),
		Cost:            models.ConvertFloat64ToPgType(req.Cost),
		QuantityInStock: pgtype.Int4{Int32: req.QuantityInStock, Valid: true},
		MinStockLevel:   pgtype.Int4{Int32: req.MinStockLevel, Valid: true},
		IsActive:        pgtype.Bool{Bool: req.IsActive, Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update part: %w", err)
	}

	return &part, nil
}

// UpdatePartStock обновляет остатки запчасти
func (s *PartService) UpdatePartStock(ctx context.Context, id uuid.UUID, req UpdatePartStockRequest) (*queries.Part, error) {
	part, err := s.queries.UpdatePartStock(ctx, queries.UpdatePartStockParams{
		ID:              models.ConvertUUIDToPgType(id),
		QuantityInStock: pgtype.Int4{Int32: req.QuantityInStock, Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update part stock: %w", err)
	}

	return &part, nil
}

// DeletePart удаляет запчасть
func (s *PartService) DeletePart(ctx context.Context, id uuid.UUID) error {
	err := s.queries.DeletePart(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return fmt.Errorf("failed to delete part: %w", err)
	}

	return nil
}

// ListPartCategories получает список категорий запчастей
func (s *PartService) ListPartCategories(ctx context.Context) ([]queries.PartCategory, error) {
	categories, err := s.queries.ListPartCategories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list part categories: %w", err)
	}

	return categories, nil
}

// ListSuppliers получает список поставщиков
func (s *PartService) ListSuppliers(ctx context.Context) ([]queries.Supplier, error) {
	suppliers, err := s.queries.ListSuppliers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list suppliers: %w", err)
	}

	return suppliers, nil
}

// SearchSuppliers ищет поставщиков по тексту
func (s *PartService) SearchSuppliers(ctx context.Context, query string) ([]queries.Supplier, error) {
	suppliers, err := s.queries.SearchSuppliers(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to search suppliers: %w", err)
	}

	return suppliers, nil
}
