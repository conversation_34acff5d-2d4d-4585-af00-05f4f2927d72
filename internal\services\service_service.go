package services

import (
	"context"
	"fmt"

	"velomasters/internal/database/queries"
	"velomasters/internal/models"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

// ServiceService предоставляет методы для работы с услугами
type ServiceService struct {
	db      *pgxpool.Pool
	queries *queries.Queries
}

// NewServiceService создает новый экземпляр ServiceService
func NewServiceService(db *pgxpool.Pool) *ServiceService {
	return &ServiceService{
		db:      db,
		queries: queries.New(db),
	}
}

// CreateServiceCategoryRequest представляет запрос на создание категории услуг
type CreateServiceCategoryRequest struct {
	Name        string  `json:"name" validate:"required"`
	Description *string `json:"description,omitempty"`
}

// CreateServiceRequest представляет запрос на создание услуги
type CreateServiceRequest struct {
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	Name            string     `json:"name" validate:"required"`
	Description     *string    `json:"description,omitempty"`
	Price           float64    `json:"price"`
	DurationMinutes int32      `json:"duration_minutes"`
	IsActive        bool       `json:"is_active"`
}

// UpdateServiceRequest представляет запрос на обновление услуги
type UpdateServiceRequest struct {
	CategoryID      *uuid.UUID `json:"category_id,omitempty"`
	Name            string     `json:"name" validate:"required"`
	Description     *string    `json:"description,omitempty"`
	Price           float64    `json:"price"`
	DurationMinutes int32      `json:"duration_minutes"`
	IsActive        bool       `json:"is_active"`
}

// CreateServiceCategory создает новую категорию услуг
func (s *ServiceService) CreateServiceCategory(ctx context.Context, req CreateServiceCategoryRequest) (*queries.ServiceCategory, error) {
	category, err := s.queries.CreateServiceCategory(ctx, queries.CreateServiceCategoryParams{
		Name:        req.Name,
		Description: models.ConvertStringToPgType(req.Description),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create service category: %w", err)
	}

	return &category, nil
}

// ListServiceCategories получает список всех категорий услуг
func (s *ServiceService) ListServiceCategories(ctx context.Context) ([]queries.ServiceCategory, error) {
	categories, err := s.queries.ListServiceCategories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list service categories: %w", err)
	}

	return categories, nil
}

// CreateService создает новую услугу
func (s *ServiceService) CreateService(ctx context.Context, req CreateServiceRequest) (*queries.Service, error) {
	var categoryID pgtype.UUID
	if req.CategoryID != nil {
		categoryID = models.ConvertUUIDToPgType(*req.CategoryID)
	}

	service, err := s.queries.CreateService(ctx, queries.CreateServiceParams{
		CategoryID:      categoryID,
		Name:            req.Name,
		Description:     models.ConvertStringToPgType(req.Description),
		Price:           models.ConvertFloat64ToPgType(req.Price),
		DurationMinutes: pgtype.Int4{Int32: req.DurationMinutes, Valid: true},
		IsActive:        pgtype.Bool{Bool: req.IsActive, Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create service: %w", err)
	}

	return &service, nil
}

// GetService получает услугу по ID
func (s *ServiceService) GetService(ctx context.Context, id uuid.UUID) (*queries.GetServiceRow, error) {
	service, err := s.queries.GetService(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get service: %w", err)
	}

	return &service, nil
}

// ListServices получает список всех активных услуг
func (s *ServiceService) ListServices(ctx context.Context) ([]queries.ListServicesRow, error) {
	services, err := s.queries.ListServices(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	return services, nil
}

// ListServicesByCategory получает список услуг по категории
func (s *ServiceService) ListServicesByCategory(ctx context.Context, categoryID uuid.UUID) ([]queries.ListServicesByCategoryRow, error) {
	services, err := s.queries.ListServicesByCategory(ctx, models.ConvertUUIDToPgType(categoryID))
	if err != nil {
		return nil, fmt.Errorf("failed to list services by category: %w", err)
	}

	return services, nil
}

// SearchServices ищет услуги по запросу
func (s *ServiceService) SearchServices(ctx context.Context, query string) ([]queries.SearchServicesRow, error) {
	services, err := s.queries.SearchServices(ctx, pgtype.Text{String: query, Valid: true})
	if err != nil {
		return nil, fmt.Errorf("failed to search services: %w", err)
	}

	return services, nil
}

// UpdateService обновляет услугу
func (s *ServiceService) UpdateService(ctx context.Context, id uuid.UUID, req UpdateServiceRequest) (*queries.Service, error) {
	var categoryID pgtype.UUID
	if req.CategoryID != nil {
		categoryID = models.ConvertUUIDToPgType(*req.CategoryID)
	}

	service, err := s.queries.UpdateService(ctx, queries.UpdateServiceParams{
		ID:              models.ConvertUUIDToPgType(id),
		CategoryID:      categoryID,
		Name:            req.Name,
		Description:     models.ConvertStringToPgType(req.Description),
		Price:           models.ConvertFloat64ToPgType(req.Price),
		DurationMinutes: pgtype.Int4{Int32: req.DurationMinutes, Valid: true},
		IsActive:        pgtype.Bool{Bool: req.IsActive, Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update service: %w", err)
	}

	return &service, nil
}

// DeleteService удаляет услугу
func (s *ServiceService) DeleteService(ctx context.Context, id uuid.UUID) error {
	err := s.queries.DeleteService(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return fmt.Errorf("failed to delete service: %w", err)
	}

	return nil
}
