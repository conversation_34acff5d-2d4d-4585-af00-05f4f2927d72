package services

import (
	"github.com/jackc/pgx/v5/pgxpool"
)

// Services содержит все сервисы приложения
type Services struct {
	Client  *ClientService
	Task    *TaskService
	Service *ServiceService
	Part    *PartService
	// Bicycle *BicycleService // Добавим позже
}

// NewServices создает новый экземпляр Services
func NewServices(db *pgxpool.Pool) *Services {
	return &Services{
		Client:  NewClientService(db),
		Task:    NewTaskService(db),
		Service: NewServiceService(db),
		Part:    NewPartService(db),
	}
}
