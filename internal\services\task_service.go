package services

import (
	"context"
	"fmt"
	"time"

	"velomasters/internal/database/queries"
	"velomasters/internal/models"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

// TaskService предоставляет методы для работы с задачами
type TaskService struct {
	db      *pgxpool.Pool
	queries *queries.Queries
}

// NewTaskService создает новый экземпляр TaskService
func NewTaskService(db *pgxpool.Pool) *TaskService {
	return &TaskService{
		db:      db,
		queries: queries.New(db),
	}
}

// CreateTaskRequest представляет запрос на создание задачи
type CreateTaskRequest struct {
	ClientID            uuid.UUID  `json:"client_id" validate:"required"`
	BicycleID           *uuid.UUID `json:"bicycle_id,omitempty"`
	Title               string     `json:"title" validate:"required"`
	Description         *string    `json:"description,omitempty"`
	Status              string     `json:"status"`
	Priority            string     `json:"priority"`
	EstimatedCost       float64    `json:"estimated_cost"`
	EstimatedCompletion *time.Time `json:"estimated_completion,omitempty"`
	Notes               *string    `json:"notes,omitempty"`
}

// UpdateTaskRequest представляет запрос на обновление задачи
type UpdateTaskRequest struct {
	Title               string     `json:"title" validate:"required"`
	Description         *string    `json:"description,omitempty"`
	Status              string     `json:"status"`
	Priority            string     `json:"priority"`
	EstimatedCost       float64    `json:"estimated_cost"`
	ActualCost          float64    `json:"actual_cost"`
	EstimatedCompletion *time.Time `json:"estimated_completion,omitempty"`
	Notes               *string    `json:"notes,omitempty"`
}

// CreateTask создает новую задачу
func (s *TaskService) CreateTask(ctx context.Context, req CreateTaskRequest) (*queries.Task, error) {
	var bicycleID pgtype.UUID
	if req.BicycleID != nil {
		bicycleID = models.ConvertUUIDToPgType(*req.BicycleID)
	}

	var estimatedCompletion pgtype.Timestamptz
	if req.EstimatedCompletion != nil {
		estimatedCompletion.Time = *req.EstimatedCompletion
		estimatedCompletion.Valid = true
	}

	task, err := s.queries.CreateTask(ctx, queries.CreateTaskParams{
		ClientID:            models.ConvertUUIDToPgType(req.ClientID),
		BicycleID:           bicycleID,
		Title:               req.Title,
		Description:         models.ConvertStringToPgType(req.Description),
		Status:              queries.NullTaskStatus{TaskStatus: queries.TaskStatus(req.Status), Valid: true},
		Priority:            queries.NullTaskPriority{TaskPriority: queries.TaskPriority(req.Priority), Valid: true},
		EstimatedCost:       models.ConvertFloat64ToPgType(req.EstimatedCost),
		EstimatedCompletion: estimatedCompletion,
		Notes:               models.ConvertStringToPgType(req.Notes),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	return &task, nil
}

// GetTask получает задачу по ID с дополнительной информацией
func (s *TaskService) GetTask(ctx context.Context, id uuid.UUID) (*queries.GetTaskRow, error) {
	task, err := s.queries.GetTask(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	return &task, nil
}

// ListTasks получает список всех задач
func (s *TaskService) ListTasks(ctx context.Context) ([]queries.ListTasksRow, error) {
	tasks, err := s.queries.ListTasks(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list tasks: %w", err)
	}

	return tasks, nil
}

// ListTasksByStatus получает список задач по статусу
func (s *TaskService) ListTasksByStatus(ctx context.Context, status string) ([]queries.ListTasksByStatusRow, error) {
	tasks, err := s.queries.ListTasksByStatus(ctx, queries.NullTaskStatus{
		TaskStatus: queries.TaskStatus(status),
		Valid:      true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list tasks by status: %w", err)
	}

	return tasks, nil
}

// ListTasksByClient получает список задач клиента
func (s *TaskService) ListTasksByClient(ctx context.Context, clientID uuid.UUID) ([]queries.ListTasksByClientRow, error) {
	tasks, err := s.queries.ListTasksByClient(ctx, models.ConvertUUIDToPgType(clientID))
	if err != nil {
		return nil, fmt.Errorf("failed to list tasks by client: %w", err)
	}

	return tasks, nil
}

// SearchTasks ищет задачи по запросу
func (s *TaskService) SearchTasks(ctx context.Context, query string) ([]queries.SearchTasksRow, error) {
	tasks, err := s.queries.SearchTasks(ctx, pgtype.Text{String: query, Valid: true})
	if err != nil {
		return nil, fmt.Errorf("failed to search tasks: %w", err)
	}

	return tasks, nil
}

// UpdateTask обновляет задачу
func (s *TaskService) UpdateTask(ctx context.Context, id uuid.UUID, req UpdateTaskRequest) (*queries.Task, error) {
	var estimatedCompletion pgtype.Timestamptz
	if req.EstimatedCompletion != nil {
		estimatedCompletion.Time = *req.EstimatedCompletion
		estimatedCompletion.Valid = true
	}

	task, err := s.queries.UpdateTask(ctx, queries.UpdateTaskParams{
		ID:                  models.ConvertUUIDToPgType(id),
		Title:               req.Title,
		Description:         models.ConvertStringToPgType(req.Description),
		Status:              queries.NullTaskStatus{TaskStatus: queries.TaskStatus(req.Status), Valid: true},
		Priority:            queries.NullTaskPriority{TaskPriority: queries.TaskPriority(req.Priority), Valid: true},
		EstimatedCost:       models.ConvertFloat64ToPgType(req.EstimatedCost),
		ActualCost:          models.ConvertFloat64ToPgType(req.ActualCost),
		EstimatedCompletion: estimatedCompletion,
		Notes:               models.ConvertStringToPgType(req.Notes),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	return &task, nil
}

// UpdateTaskStatus обновляет статус задачи
func (s *TaskService) UpdateTaskStatus(ctx context.Context, id uuid.UUID, status string) (*queries.Task, error) {
	task, err := s.queries.UpdateTaskStatus(ctx, queries.UpdateTaskStatusParams{
		ID:     models.ConvertUUIDToPgType(id),
		Status: queries.NullTaskStatus{TaskStatus: queries.TaskStatus(status), Valid: true},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update task status: %w", err)
	}

	return &task, nil
}

// StartTask запускает задачу в работу
func (s *TaskService) StartTask(ctx context.Context, id uuid.UUID) (*queries.Task, error) {
	task, err := s.queries.StartTask(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to start task: %w", err)
	}

	return &task, nil
}

// CompleteTask завершает задачу
func (s *TaskService) CompleteTask(ctx context.Context, id uuid.UUID) (*queries.Task, error) {
	task, err := s.queries.CompleteTask(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return nil, fmt.Errorf("failed to complete task: %w", err)
	}

	return &task, nil
}

// DeleteTask удаляет задачу
func (s *TaskService) DeleteTask(ctx context.Context, id uuid.UUID) error {
	err := s.queries.DeleteTask(ctx, models.ConvertUUIDToPgType(id))
	if err != nil {
		return fmt.Errorf("failed to delete task: %w", err)
	}

	return nil
}

// AddServiceToTask добавляет услугу к задаче
func (s *TaskService) AddServiceToTask(ctx context.Context, taskID, serviceID uuid.UUID, quantity int32, price float64, notes *string) error {
	_, err := s.queries.AddServiceToTask(ctx, queries.AddServiceToTaskParams{
		TaskID:    models.ConvertUUIDToPgType(taskID),
		ServiceID: models.ConvertUUIDToPgType(serviceID),
		Quantity:  pgtype.Int4{Int32: quantity, Valid: true},
		Price:     models.ConvertFloat64ToPgType(price),
		Notes:     models.ConvertStringToPgType(notes),
	})
	if err != nil {
		return fmt.Errorf("failed to add service to task: %w", err)
	}

	return nil
}

// GetTaskServices получает список услуг задачи
func (s *TaskService) GetTaskServices(ctx context.Context, taskID uuid.UUID) ([]queries.GetTaskServicesRow, error) {
	services, err := s.queries.GetTaskServices(ctx, models.ConvertUUIDToPgType(taskID))
	if err != nil {
		return nil, fmt.Errorf("failed to get task services: %w", err)
	}

	return services, nil
}

// AddPartToTask добавляет запчасть к задаче
func (s *TaskService) AddPartToTask(ctx context.Context, taskID, partID uuid.UUID, quantity int32, price float64, notes *string) error {
	_, err := s.queries.AddPartToTask(ctx, queries.AddPartToTaskParams{
		TaskID:   models.ConvertUUIDToPgType(taskID),
		PartID:   models.ConvertUUIDToPgType(partID),
		Quantity: quantity,
		Price:    models.ConvertFloat64ToPgType(price),
		Notes:    models.ConvertStringToPgType(notes),
	})
	if err != nil {
		return fmt.Errorf("failed to add part to task: %w", err)
	}

	return nil
}

// GetTaskParts получает список запчастей задачи
func (s *TaskService) GetTaskParts(ctx context.Context, taskID uuid.UUID) ([]queries.GetTaskPartsRow, error) {
	parts, err := s.queries.GetTaskParts(ctx, models.ConvertUUIDToPgType(taskID))
	if err != nil {
		return nil, fmt.Errorf("failed to get task parts: %w", err)
	}

	return parts, nil
}
