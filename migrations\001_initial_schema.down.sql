-- Удаление таблиц в обратном порядке (с учетом зависимостей)
DROP TABLE IF EXISTS task_parts;
DROP TABLE IF EXISTS task_services;
DROP TABLE IF EXISTS tasks;
DROP TABLE IF EXISTS parts;
DROP TABLE IF EXISTS part_categories;
DROP TABLE IF EXISTS services;
DROP TABLE IF EXISTS service_categories;
DROP TABLE IF EXISTS suppliers;
DROP TABLE IF EXISTS bicycles;
DROP TABLE IF EXISTS clients;

-- Удаление типов
DROP TYPE IF EXISTS task_priority;
DROP TYPE IF EXISTS task_status;
