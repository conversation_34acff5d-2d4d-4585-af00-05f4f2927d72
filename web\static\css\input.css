@tailwind base;
@tailwind components;
@tailwind utilities;

/* Импорт шрифтов */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Базовые стили */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
  }

  body {
    @apply antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200;
  }

  /* Кастомные стили для скроллбара - светлая тема */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f3f4f6;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }

  /* Кастомные стили для скроллбара - темная тема */
  .dark ::-webkit-scrollbar-track {
    background-color: #374151;
  }

  .dark ::-webkit-scrollbar-thumb {
    background-color: #6b7280;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }
}

/* Компоненты */
@layer components {
  /* Кнопки */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Формы */
  .form-input {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .form-select {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .form-textarea {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .form-checkbox {
    @apply rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500;
  }

  .form-radio {
    @apply border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500;
  }

  /* Карточки */
  .card {
    @apply bg-white/80 dark:bg-gray-800/60 rounded-lg shadow-soft border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm;
  }

  .card-hover {
    @apply card hover:shadow-md dark:hover:shadow-gray-900/20 hover:bg-white/90 dark:hover:bg-gray-800/70 transition-all duration-200;
  }

  .stats-card {
    @apply bg-gradient-to-br from-white/90 to-gray-50/90 dark:from-gray-800/70 dark:to-gray-900/70 rounded-lg shadow-soft border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200/50 dark:border-gray-700/50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200/50 dark:border-gray-700/50 bg-gray-50/80 dark:bg-gray-700/60 rounded-b-lg;
  }
  
  /* Бейджи */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-warning {
    @apply badge bg-accent-100 text-accent-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  /* Статусы задач */
  .status-pending {
    @apply badge-warning;
  }
  
  .status-in-progress {
    @apply badge-primary;
  }
  
  .status-completed {
    @apply badge-success;
  }
  
  .status-cancelled {
    @apply badge-secondary;
  }
  
  /* Приоритеты */
  .priority-low {
    @apply badge-secondary;
  }
  
  .priority-medium {
    @apply badge-primary;
  }
  
  .priority-high {
    @apply badge-warning;
  }
  
  .priority-urgent {
    @apply badge-danger;
  }
  
  /* Модальные окна */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-80 transition-opacity z-40;
  }

  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-content {
    @apply relative bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all sm:max-w-lg sm:w-full;
  }

  /* Навигация */
  .nav-link {
    @apply text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 px-3 py-2 rounded-md text-sm font-medium transition-colors;
  }

  .nav-link-active {
    @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
  }

  /* Таблицы */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }

  .table-row-hover {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors;
  }

  /* Уведомления (Toasts) */
  .notification-toast {
    @apply max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 dark:ring-gray-700 p-4;
  }
}

/* HTMX стили */
@layer utilities {
  .htmx-indicator {
    @apply opacity-0 transition-opacity duration-200;
  }
  
  .htmx-request .htmx-indicator {
    @apply opacity-100;
  }
  
  .htmx-request.htmx-indicator {
    @apply opacity-100;
  }
  
  .htmx-settling {
    @apply transition-all duration-300;
  }
  
  .htmx-swapping {
    @apply transition-all duration-300;
  }
  
  /* Анимации для HTMX */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
  
  .slide-down {
    @apply animate-slide-down;
  }
  
  /* Утилиты для состояний */
  .loading {
    @apply animate-pulse;
  }
  
  .error {
    @apply border-danger-300 bg-danger-50;
  }
  
  .success {
    @apply border-secondary-300 bg-secondary-50;
  }
}
