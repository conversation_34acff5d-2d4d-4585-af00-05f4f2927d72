// VeloMasters - Основной JavaScript файл

// Утилиты для уведомлений
const notifications = {
  container: null,
  
  init() {
    this.container = document.getElementById('notifications');
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'notifications';
      this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
      document.body.appendChild(this.container);
    }
  },
  
  show(message, type = 'info', duration = 5000) {
    this.init();
    
    const notification = document.createElement('div');
    notification.className = `
      max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto 
      ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all 
      duration-300 ease-in-out translate-x-full opacity-0
    `;
    
    const colors = {
      success: 'text-green-500',
      error: 'text-red-500',
      warning: 'text-yellow-500',
      info: 'text-blue-500'
    };
    
    notification.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 ${colors[type] || colors.info}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">${message}</p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="this.closest('.notification').remove()">
              <span class="sr-only">Закрыть</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;
    
    notification.classList.add('notification');
    this.container.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
      notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);
    
    // Автоматическое скрытие
    if (duration > 0) {
      setTimeout(() => {
        this.hide(notification);
      }, duration);
    }
    
    return notification;
  },
  
  hide(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  },
  
  success(message, duration) {
    return this.show(message, 'success', duration);
  },
  
  error(message, duration) {
    return this.show(message, 'error', duration);
  },
  
  warning(message, duration) {
    return this.show(message, 'warning', duration);
  },
  
  info(message, duration) {
    return this.show(message, 'info', duration);
  }
};

// Утилиты для модальных окон
const modal = {
  open(content, options = {}) {
    const overlay = document.createElement('div');
    overlay.className = 'modal-overlay';
    overlay.onclick = () => this.close(overlay);
    
    const container = document.createElement('div');
    container.className = 'modal-container flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0';
    
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 transition-opacity';
    backdrop.onclick = () => this.close(overlay);
    
    const modal = document.createElement('div');
    modal.className = `
      modal-content inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl 
      transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full
    `;
    modal.innerHTML = content;
    
    container.appendChild(backdrop);
    container.appendChild(modal);
    overlay.appendChild(container);
    document.body.appendChild(overlay);
    
    // Анимация появления
    setTimeout(() => {
      overlay.classList.add('opacity-100');
      modal.classList.add('scale-100');
    }, 10);
    
    return overlay;
  },
  
  close(overlay) {
    overlay.classList.remove('opacity-100');
    overlay.querySelector('.modal-content').classList.remove('scale-100');
    
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 300);
  }
};

// Утилиты для форм
const forms = {
  serialize(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
      if (data[key]) {
        if (Array.isArray(data[key])) {
          data[key].push(value);
        } else {
          data[key] = [data[key], value];
        }
      } else {
        data[key] = value;
      }
    }
    
    return data;
  },
  
  validate(form) {
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
      if (!input.value.trim()) {
        input.classList.add('border-red-300', 'bg-red-50');
        isValid = false;
      } else {
        input.classList.remove('border-red-300', 'bg-red-50');
      }
    });
    
    return isValid;
  },
  
  reset(form) {
    form.reset();
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      input.classList.remove('border-red-300', 'bg-red-50', 'border-green-300', 'bg-green-50');
    });
  }
};

// HTMX события
document.addEventListener('htmx:beforeRequest', function(event) {
  // Показываем индикатор загрузки
  const indicator = event.target.querySelector('.htmx-indicator');
  if (indicator) {
    indicator.classList.remove('opacity-0');
  }
});

document.addEventListener('htmx:afterRequest', function(event) {
  // Скрываем индикатор загрузки
  const indicator = event.target.querySelector('.htmx-indicator');
  if (indicator) {
    indicator.classList.add('opacity-0');
  }
  
  // Показываем уведомления на основе ответа
  if (event.detail.xhr.status >= 200 && event.detail.xhr.status < 300) {
    const successMessage = event.detail.xhr.getResponseHeader('X-Success-Message');
    if (successMessage) {
      notifications.success(successMessage);
    }
  } else {
    const errorMessage = event.detail.xhr.getResponseHeader('X-Error-Message') || 'Произошла ошибка';
    notifications.error(errorMessage);
  }
});

document.addEventListener('htmx:responseError', function(event) {
  notifications.error('Ошибка сервера. Попробуйте позже.');
});

document.addEventListener('htmx:sendError', function(event) {
  notifications.error('Ошибка сети. Проверьте подключение.');
});

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
  notifications.init();
  
  // Автоматическое скрытие алертов
  const alerts = document.querySelectorAll('[data-auto-dismiss]');
  alerts.forEach(alert => {
    const delay = parseInt(alert.dataset.autoDismiss) || 5000;
    setTimeout(() => {
      alert.style.opacity = '0';
      setTimeout(() => alert.remove(), 300);
    }, delay);
  });
});

// Экспорт для глобального использования
window.VeloMasters = {
  notifications,
  modal,
  forms
};
