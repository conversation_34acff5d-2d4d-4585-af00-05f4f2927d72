package components

templ StatCard(title, value, textColor, bgColor string) {
	<div class="stats-card overflow-hidden">
		<div class="p-5">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<div class={ "w-8 h-8 rounded-md flex items-center justify-center", bgColor, "dark:bg-opacity-20" }>
						<svg class={ "w-5 h-5", textColor } fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
						</svg>
					</div>
				</div>
				<div class="ml-5 w-0 flex-1">
					<dl>
						<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ title }</dt>
						<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ value }</dd>
					</dl>
				</div>
			</div>
		</div>
	</div>
}

templ QuickActionCard(title, description, href, iconPath string) {
	<a href={ templ.URL(href) } class="group relative card-hover p-6 transition-all duration-200">
		<div>
			<span class="rounded-lg inline-flex p-3 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 ring-4 ring-white dark:ring-gray-800">
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" d={ iconPath }></path>
				</svg>
			</span>
		</div>
		<div class="mt-4">
			<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
				{ title }
			</h3>
			<p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
				{ description }
			</p>
		</div>
		<span class="pointer-events-none absolute top-6 right-6 text-gray-300 dark:text-gray-600 group-hover:text-gray-400 dark:group-hover:text-gray-500 transition-colors" aria-hidden="true">
			<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
				<path d="M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z"/>
			</svg>
		</span>
	</a>
}
