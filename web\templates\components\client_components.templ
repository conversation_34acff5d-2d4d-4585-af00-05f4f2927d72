package components

import "velomasters/internal/database/queries"

// ClientCard компонент для отображения карточки клиента
templ ClientCard(client queries.Client) {
	<div class="card mb-4 client-card" data-client-id={ client.ID.String() }>
		<div class="card-body">
			<div class="flex justify-between items-start mb-3">
				<div class="flex-1">
					<h3 class="text-lg font-semibold text-gray-900 mb-1">{ client.Name }</h3>
					<div class="space-y-1 text-sm text-gray-600">
						if client.Phone.Valid {
							<div class="flex items-center">
								<svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
								</svg>
								<a href={ templ.URL("tel:" + client.Phone.String) } class="text-blue-600 hover:text-blue-800">
									{ client.Phone.String }
								</a>
							</div>
						}
						if client.Email.Valid {
							<div class="flex items-center">
								<svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
								</svg>
								<a href={ templ.URL("mailto:" + client.Email.String) } class="text-blue-600 hover:text-blue-800">
									{ client.Email.String }
								</a>
							</div>
						}
						if client.Address.Valid {
							<div class="flex items-center">
								<svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
								</svg>
								<span>{ client.Address.String }</span>
							</div>
						}
					</div>
				</div>
				<div class="flex items-center space-x-2 ml-4">
					<span class="text-xs text-gray-500">
						Клиент с { formatDate(client.CreatedAt) }
					</span>
				</div>
			</div>
			
			<div class="flex justify-between items-center pt-3 border-t border-gray-200">
				<div class="text-sm text-gray-600">
					<span class="font-medium">Активных задач:</span>
					<span 
						class="ml-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
						hx-get={ "/api/clients/" + client.ID.String() + "/tasks/count" }
						hx-trigger="load"
					>
						...
					</span>
				</div>
				<div class="flex space-x-2">
					<button 
						class="btn btn-sm btn-outline"
						hx-get={ "/api/clients/" + client.ID.String() + "/edit" }
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('client-modal').classList.remove('hidden')"
					>
						Редактировать
					</button>
					<button 
						class="btn btn-sm btn-primary"
						hx-get={ "/api/clients/" + client.ID.String() + "/tasks" }
						hx-target="#tasks-container"
						hx-trigger="click"
					>
						Задачи
					</button>
				</div>
			</div>
		</div>
	</div>
}

// ClientList компонент для отображения списка клиентов
templ ClientList(clients []queries.Client, showEmpty bool) {
	<div id="clients-container">
		if len(clients) > 0 {
			for _, client := range clients {
				@ClientCard(client)
			}
		} else if showEmpty {
			@EmptyState("Нет клиентов", "Пока нет зарегистрированных клиентов. Добавьте первого клиента для начала работы.", "Добавить клиента", "/web/clients/new")
		}
	</div>
}

// ClientForm компонент для формы создания/редактирования клиента
templ ClientForm(client *queries.Client, isEdit bool) {
	<form 
		if isEdit {
			hx-put={ "/api/clients/" + client.ID.String() }
		} else {
			hx-post="/api/clients"
		}
		hx-target="#clients-container"
		hx-swap="afterbegin"
		hx-on::after-request="if(event.detail.successful) { document.getElementById('client-modal').classList.add('hidden'); VeloMasters.forms.reset(this); }"
		class="space-y-4"
	>
		@FormField("Имя клиента", "text", "name", getClientValue(client, "name"), "Введите имя клиента", true, nil)
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			@FormField("Телефон", "tel", "phone", getClientValue(client, "phone"), "+7 (999) 123-45-67", true, nil)
			@FormField("Email", "email", "email", getClientValue(client, "email"), "<EMAIL>", false, nil)
		</div>
		
		@FormField("Адрес", "text", "address", getClientValue(client, "address"), "Адрес клиента", false, nil)
		
		@FormField("Примечания", "textarea", "notes", getClientValue(client, "notes"), "Дополнительная информация о клиенте", false, nil)
		
		<div class="flex justify-end space-x-3 pt-4">
			<button 
				type="button" 
				class="btn btn-outline"
				onclick="document.getElementById('client-modal').classList.add('hidden')"
			>
				Отмена
			</button>
			if isEdit {
				@FormButton("Сохранить", "submit", "primary", false, false)
			} else {
				@FormButton("Добавить клиента", "submit", "primary", false, false)
			}
		</div>
	</form>
}

// ClientFilters компонент для фильтров клиентов
templ ClientFilters() {
	<div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4 mb-6">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
			<div class="md:col-span-2">
				@SearchBox("Поиск клиентов...", "", "/api/clients/search")
			</div>
			<div>
				<button 
					class="btn btn-primary w-full"
					hx-get="/api/clients/new"
					hx-target="#modal-content"
					hx-trigger="click"
					onclick="document.getElementById('client-modal').classList.remove('hidden')"
				>
					+ Новый клиент
				</button>
			</div>
		</div>
	</div>
}

// ClientStats компонент для статистики клиентов
templ ClientStats() {
	<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
		<div 
			class="card"
			hx-get="/api/clients/stats/total"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Всего клиентов</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/clients/stats/active"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Активные</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/clients/stats/new"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Новые (месяц)</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/clients/stats/revenue"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Доход (месяц)</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
	</div>
}



func getClientValue(client *queries.Client, field string) string {
	if client == nil {
		return ""
	}
	
	switch field {
	case "name":
		return client.Name
	case "phone":
		return client.Phone.String
	case "email":
		return client.Email.String
	case "address":
		return client.Address.String
	case "notes":
		return client.Notes.String
	default:
		return ""
	}
}
