package components

templ Footer() {
	<footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto transition-colors duration-200">
		<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
			<div class="flex flex-col md:flex-row justify-between items-center">
				<div class="flex items-center space-x-2 mb-4 md:mb-0">
					<svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
					</svg>
					<span class="text-lg font-semibold text-gray-900 dark:text-gray-100">VeloMasters</span>
				</div>

				<div class="text-sm text-gray-500 dark:text-gray-400">
					© 2025 VeloMasters. Система учета для велосипедной мастерской от <a href="https://revive-it.ru" class="text-blue-600 dark:text-blue-400 hover:underline">revive-it.ru</a>
				</div>
			</div>
		</div>
	</footer>
}
