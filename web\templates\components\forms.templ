package components

import "strconv"

// FormField компонент для полей формы
templ FormField(label string, fieldType string, name string, value string, placeholder string, required bool, errors []string) {
	<div class="mb-4">
		if label != "" {
			<label for={ name } class="block text-sm font-medium text-gray-700 mb-2">
				{ label }
				if required {
					<span class="text-red-500">*</span>
				}
			</label>
		}
		
		switch fieldType {
		case "textarea":
			<textarea
				id={ name }
				name={ name }
				class={ "form-textarea", getFieldClasses(errors) }
				placeholder={ placeholder }
				if required {
					required
				}
			>{ value }</textarea>
		case "select":
			<select
				id={ name }
				name={ name }
				class={ "form-select", getFieldClasses(errors) }
				if required {
					required
				}
			>
				{ children... }
			</select>
		default:
			<input
				type={ fieldType }
				id={ name }
				name={ name }
				value={ value }
				class={ "form-input", getFieldClasses(errors) }
				placeholder={ placeholder }
				if required {
					required
				}
			/>
		}
		
		if len(errors) > 0 {
			<div class="mt-1">
				for _, err := range errors {
					<p class="text-sm text-red-600">{ err }</p>
				}
			</div>
		}
	</div>
}

// Функция для получения CSS классов поля в зависимости от ошибок
func getFieldClasses(errors []string) string {
	if len(errors) > 0 {
		return "border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-500"
	}
	return ""
}

// FormButton компонент для кнопок формы
templ FormButton(text string, buttonType string, variant string, loading bool, disabled bool) {
	<button
		type={ buttonType }
		class={ "btn", getButtonVariant(variant) }
		if disabled || loading {
			disabled
		}
	>
		if loading {
			@LoadingSpinner("sm")
			<span class="ml-2">Загрузка...</span>
		} else {
			{ text }
		}
	</button>
}

// Функция для получения варианта кнопки
func getButtonVariant(variant string) string {
	switch variant {
	case "primary":
		return "btn-primary"
	case "secondary":
		return "btn-secondary"
	case "success":
		return "btn-success"
	case "danger":
		return "btn-danger"
	case "outline":
		return "btn-outline"
	default:
		return "btn-primary"
	}
}

// Modal компонент для модальных окон
templ Modal(id string, title string, size string) {
	<div 
		id={ id }
		class="modal-overlay hidden"
		x-data="{ open: false }"
		x-show="open"
		x-transition:enter="transition ease-out duration-300"
		x-transition:enter-start="opacity-0"
		x-transition:enter-end="opacity-100"
		x-transition:leave="transition ease-in duration-200"
		x-transition:leave-start="opacity-100"
		x-transition:leave-end="opacity-0"
		@click.self="open = false"
	>
		<div class="modal-container flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
			<div class="fixed inset-0 transition-opacity" @click="open = false"></div>
			
			<span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
			
			<div 
				class={ "modal-content", getModalSize(size) }
				x-transition:enter="transition ease-out duration-300"
				x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
				x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
				x-transition:leave="transition ease-in duration-200"
				x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
				x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
			>
				<div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
					<div class="sm:flex sm:items-start">
						<div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
							<h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
								{ title }
							</h3>
							<div class="mt-2">
								{ children... }
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

// Функция для получения размера модального окна
func getModalSize(size string) string {
	switch size {
	case "sm":
		return "sm:max-w-sm"
	case "lg":
		return "sm:max-w-4xl"
	case "xl":
		return "sm:max-w-6xl"
	default:
		return "sm:max-w-lg"
	}
}

// SearchBox компонент для поиска
templ SearchBox(placeholder string, value string, endpoint string) {
	<div class="relative">
		<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
			<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
			</svg>
		</div>
		<input
			type="text"
			name="search"
			value={ value }
			class="form-input pl-10"
			placeholder={ placeholder }
			hx-get={ endpoint }
			hx-trigger="keyup changed delay:300ms"
			hx-target="#search-results"
			hx-indicator="#search-loading"
		/>
		<div id="search-loading" class="htmx-indicator absolute inset-y-0 right-0 pr-3 flex items-center">
			@LoadingSpinner("sm")
		</div>
	</div>
}

// Pagination компонент для пагинации
templ Pagination(currentPage int, totalPages int, baseUrl string) {
	if totalPages > 1 {
		<nav class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
			<div class="flex flex-1 justify-between sm:hidden">
				if currentPage > 1 {
					<a href={ templ.URL(baseUrl + "?page=" + strconv.Itoa(currentPage-1)) } class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
						Предыдущая
					</a>
				}
				if currentPage < totalPages {
					<a href={ templ.URL(baseUrl + "?page=" + strconv.Itoa(currentPage+1)) } class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
						Следующая
					</a>
				}
			</div>
			<div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
				<div>
					<p class="text-sm text-gray-700">
						Страница <span class="font-medium">{ strconv.Itoa(currentPage) }</span> из <span class="font-medium">{ strconv.Itoa(totalPages) }</span>
					</p>
				</div>
				<div>
					<nav class="isolate inline-flex -space-x-px rounded-md shadow-sm">
						if currentPage > 1 {
							<a href={ templ.URL(baseUrl + "?page=" + strconv.Itoa(currentPage-1)) } class="relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50">
								<span class="sr-only">Предыдущая</span>
								<svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
								</svg>
							</a>
						}
						
						for i := 1; i <= totalPages; i++ {
							if i == currentPage {
								<span class="relative z-10 inline-flex items-center border border-primary-500 bg-primary-50 px-4 py-2 text-sm font-medium text-primary-600">
									{ strconv.Itoa(i) }
								</span>
							} else {
								<a href={ templ.URL(baseUrl + "?page=" + strconv.Itoa(i)) } class="relative inline-flex items-center border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50">
									{ strconv.Itoa(i) }
								</a>
							}
						}
						
						if currentPage < totalPages {
							<a href={ templ.URL(baseUrl + "?page=" + strconv.Itoa(currentPage+1)) } class="relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50">
								<span class="sr-only">Следующая</span>
								<svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
								</svg>
							</a>
						}
					</nav>
				</div>
			</div>
		</nav>
	}
}
