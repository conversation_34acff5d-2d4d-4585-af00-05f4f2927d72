package components

templ Header() {
	<header class="bg-white dark:bg-gray-800 shadow dark:shadow-gray-700/20 transition-colors duration-200 mt-4">
		<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
			<div class="flex h-16 justify-between items-center">
				<!-- Логотип -->
				<div class="flex items-center">
					<a href="/" class="flex items-center space-x-2">
						<svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
						</svg>
						<span class="text-xl font-bold text-gray-900 dark:text-gray-100">VeloMasters</span>
					</a>
				</div>

				<!-- Навигация -->
				<nav class="hidden md:flex space-x-8">
					<a href="/" class="nav-link">
						Главная
					</a>
					<a href="/web/tasks" class="nav-link">
						Задачи
					</a>
					<a href="/web/clients" class="nav-link">
						Клиенты
					</a>
					<a href="/web/services" class="nav-link">
						Услуги
					</a>
					<a href="/web/parts" class="nav-link">
						Запчасти
					</a>
				</nav>

				<!-- Переключатель темы и мобильное меню -->
				<div class="flex items-center space-x-4">
					<!-- Переключатель темы -->
					<button
						@click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)"
						class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
						title="Переключить тему"
					>
						<svg x-show="!darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
						</svg>
						<svg x-show="darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
						</svg>
					</button>

					<!-- Мобильное меню -->
					<div class="md:hidden" x-data="{ open: false }">
						<button @click="open = !open" class="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 p-2">
							<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path x-show="!open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
								<path x-show="open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
							</svg>
						</button>

						<div x-show="open" x-transition class="absolute top-16 right-4 bg-white dark:bg-gray-800 shadow-lg rounded-md py-2 w-48 z-50 border dark:border-gray-700">
							<a href="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Главная</a>
							<a href="/web/tasks" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Задачи</a>
							<a href="/web/clients" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Клиенты</a>
							<a href="/web/services" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Услуги</a>
							<a href="/web/parts" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Запчасти</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</header>
}
