package components

import "fmt"
import "github.com/jackc/pgx/v5/pgtype"
import "velomasters/internal/database/queries"

// Общие вспомогательные функции для всех компонентов

func formatDate(ts pgtype.Timestamptz) string {
	if !ts.Valid {
		return ""
	}
	return ts.Time.Format("02.01.2006")
}

func formatPrice(price pgtype.Numeric) string {
	if !price.Valid {
		return "0.00"
	}
	// Простое преобразование для отображения
	f64, _ := price.Float64Value()
	return fmt.Sprintf("%.2f", f64.Float64)
}

func formatDuration(minutes int32) string {
	if minutes < 60 {
		return fmt.Sprintf("%d мин", minutes)
	}
	hours := minutes / 60
	remainingMinutes := minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d ч", hours)
	}
	return fmt.Sprintf("%d ч %d мин", hours, remainingMinutes)
}

func formatQuantity(quantity pgtype.Int4) string {
	if !quantity.Valid {
		return "0"
	}
	return fmt.Sprintf("%d", quantity.Int32)
}

func isPartActive(part queries.ListPartsRow) bool {
	return part.IsActive.Valid && part.IsActive.Bool
}

func isServiceActive(service queries.Service) bool {
	return service.IsActive.Valid && service.IsActive.Bool
}

// Простой компонент для использования templ импорта
templ HelperComponent() {
	<div style="display: none;"></div>
}
