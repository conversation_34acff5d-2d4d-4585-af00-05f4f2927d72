// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.920
package components

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import "fmt"
import "github.com/jackc/pgx/v5/pgtype"
import "velomasters/internal/database/queries"

// Общие вспомогательные функции для всех компонентов

func formatDate(ts pgtype.Timestamptz) string {
	if !ts.Valid {
		return ""
	}
	return ts.Time.Format("02.01.2006")
}

func formatPrice(price pgtype.Numeric) string {
	if !price.Valid {
		return "0.00"
	}
	// Простое преобразование для отображения
	f64, _ := price.Float64Value()
	return fmt.Sprintf("%.2f", f64.Float64)
}

func formatDuration(minutes int32) string {
	if minutes < 60 {
		return fmt.Sprintf("%d мин", minutes)
	}
	hours := minutes / 60
	remainingMinutes := minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d ч", hours)
	}
	return fmt.Sprintf("%d ч %d мин", hours, remainingMinutes)
}

func formatQuantity(quantity pgtype.Int4) string {
	if !quantity.Valid {
		return "0"
	}
	return fmt.Sprintf("%d", quantity.Int32)
}

func isPartActive(part queries.ListPartsRow) bool {
	return part.IsActive.Valid && part.IsActive.Bool
}

func isServiceActive(service queries.Service) bool {
	return service.IsActive.Valid && service.IsActive.Bool
}

// Простой компонент для использования templ импорта
func HelperComponent() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<div style=\"display: none;\"></div>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
