package components

// TaskModal модальное окно для задач
templ TaskModal() {
	<div id="task-modal" class="modal-overlay hidden">
		<div class="modal-container">
			<div class="modal-content">
				<div class="flex justify-between items-center mb-4">
					<h2 class="text-xl font-semibold text-gray-900">Задача</h2>
					<button 
						type="button" 
						class="text-gray-400 hover:text-gray-600"
						onclick="document.getElementById('task-modal').classList.add('hidden')"
					>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>
				<div id="modal-content">
					<!-- Содержимое загружается через HTMX -->
					@LoadingSpinner("md")
				</div>
			</div>
		</div>
	</div>
}

// ClientModal модальное окно для клиентов
templ ClientModal() {
	<div id="client-modal" class="modal-overlay hidden">
		<div class="modal-container">
			<div class="modal-content">
				<div class="flex justify-between items-center mb-4">
					<h2 class="text-xl font-semibold text-gray-900">Клиент</h2>
					<button 
						type="button" 
						class="text-gray-400 hover:text-gray-600"
						onclick="document.getElementById('client-modal').classList.add('hidden')"
					>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>
				<div id="modal-content">
					<!-- Содержимое загружается через HTMX -->
					@LoadingSpinner("md")
				</div>
			</div>
		</div>
	</div>
}

// ServiceModal модальное окно для услуг
templ ServiceModal() {
	<div id="service-modal" class="modal-overlay hidden">
		<div class="modal-container">
			<div class="modal-content">
				<div class="flex justify-between items-center mb-4">
					<h2 class="text-xl font-semibold text-gray-900">Услуга</h2>
					<button 
						type="button" 
						class="text-gray-400 hover:text-gray-600"
						onclick="document.getElementById('service-modal').classList.add('hidden')"
					>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>
				<div id="modal-content">
					<!-- Содержимое загружается через HTMX -->
					@LoadingSpinner("md")
				</div>
			</div>
		</div>
	</div>
}

// ConfirmModal модальное окно для подтверждения действий
templ ConfirmModal() {
	<div id="confirm-modal" class="modal-overlay hidden">
		<div class="modal-container max-w-md">
			<div class="modal-content">
				<div class="flex items-center mb-4">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
						</svg>
					</div>
					<div class="ml-4">
						<h3 class="text-lg font-medium text-gray-900">Подтверждение действия</h3>
					</div>
				</div>
				<div id="confirm-content" class="mb-6">
					<p class="text-sm text-gray-600">Вы уверены, что хотите выполнить это действие?</p>
				</div>
				<div class="flex justify-end space-x-3">
					<button 
						type="button" 
						class="btn btn-outline"
						onclick="document.getElementById('confirm-modal').classList.add('hidden')"
					>
						Отмена
					</button>
					<button 
						id="confirm-action"
						type="button" 
						class="btn btn-danger"
					>
						Подтвердить
					</button>
				</div>
			</div>
		</div>
	</div>
}

// NotificationToast уведомление-тост
templ NotificationToast(message string, notificationType string) {
	<div 
		class={ "notification-toast", getNotificationClass(notificationType) }
		x-data="{ show: true }"
		x-show="show"
		x-transition:enter="transition ease-out duration-300"
		x-transition:enter-start="opacity-0 transform translate-y-2"
		x-transition:enter-end="opacity-100 transform translate-y-0"
		x-transition:leave="transition ease-in duration-200"
		x-transition:leave-start="opacity-100 transform translate-y-0"
		x-transition:leave-end="opacity-0 transform translate-y-2"
		x-init="setTimeout(() => show = false, 5000)"
	>
		<div class="flex items-center">
			<div class="flex-shrink-0">
				@NotificationIcon(notificationType)
			</div>
			<div class="ml-3">
				<p class="text-sm font-medium">{ message }</p>
			</div>
			<div class="ml-auto pl-3">
				<button 
					type="button" 
					class="inline-flex text-gray-400 hover:text-gray-600"
					@click="show = false"
				>
					<svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
				</button>
			</div>
		</div>
	</div>
}

// NotificationIcon иконка для уведомления
templ NotificationIcon(notificationType string) {
	switch notificationType {
	case "success":
		<svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
		</svg>
	case "error":
		<svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
		</svg>
	case "warning":
		<svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
		</svg>
	case "info":
		<svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
		</svg>
	default:
		<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
		</svg>
	}
}

// NotificationContainer контейнер для уведомлений
templ NotificationContainer() {
	<div 
		id="notification-container" 
		class="fixed top-4 right-4 z-50 space-y-2"
		hx-swap-oob="true"
	>
		<!-- Уведомления добавляются сюда через HTMX -->
	</div>
}

// QuickActions панель быстрых действий
templ QuickActions() {
	<div class="fixed bottom-6 right-6 z-40">
		<div class="flex flex-col space-y-3">
			<button 
				class="btn btn-primary btn-lg rounded-full shadow-lg"
				hx-get="/api/tasks/new"
				hx-target="#modal-content"
				hx-trigger="click"
				onclick="document.getElementById('task-modal').classList.remove('hidden')"
				title="Новая задача"
			>
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
				</svg>
			</button>
			<button 
				class="btn btn-secondary btn-lg rounded-full shadow-lg"
				hx-get="/api/clients/new"
				hx-target="#modal-content"
				hx-trigger="click"
				onclick="document.getElementById('client-modal').classList.remove('hidden')"
				title="Новый клиент"
			>
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
				</svg>
			</button>
		</div>
	</div>
}

// Вспомогательные функции
func getNotificationClass(notificationType string) string {
	switch notificationType {
	case "success":
		return "border-l-4 border-green-400 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200"
	case "error":
		return "border-l-4 border-red-400 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200"
	case "warning":
		return "border-l-4 border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200"
	case "info":
		return "border-l-4 border-blue-400 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200"
	default:
		return "border-l-4 border-gray-400 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
	}
}

// PartModal модальное окно для запчастей
templ PartModal() {
	<div id="part-modal" class="modal-overlay hidden">
		<div class="modal-container">
			<div class="modal-content">
				<div class="flex justify-between items-center mb-4">
					<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Запчасть</h2>
					<button
						type="button"
						class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
						onclick="document.getElementById('part-modal').classList.add('hidden')"
					>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>
				<div id="modal-content">
					<!-- Содержимое загружается через HTMX -->
					@LoadingSpinner("md")
				</div>
			</div>
		</div>
	</div>
}
