package components

// Alert компонент для отображения уведомлений
templ Alert(message string, alertType string, dismissible bool) {
	<div 
		class={ "alert", getAlertClasses(alertType) }
		if dismissible {
			data-auto-dismiss="5000"
		}
		role="alert"
	>
		<div class="flex">
			<div class="flex-shrink-0">
				@getAlertIcon(alertType)
			</div>
			<div class="ml-3">
				<p class="text-sm font-medium">{ message }</p>
			</div>
			if dismissible {
				<div class="ml-auto pl-3">
					<div class="-mx-1.5 -my-1.5">
						<button 
							type="button" 
							class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
							onclick="this.closest('.alert').remove()"
						>
							<span class="sr-only">Закрыть</span>
							<svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
								<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
							</svg>
						</button>
					</div>
				</div>
			}
		</div>
	</div>
}

// Функция для получения CSS классов в зависимости от типа уведомления
func getAlertClasses(alertType string) string {
	switch alertType {
	case "success":
		return "bg-green-50 border border-green-200 text-green-800 rounded-md p-4"
	case "error":
		return "bg-red-50 border border-red-200 text-red-800 rounded-md p-4"
	case "warning":
		return "bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4"
	case "info":
		return "bg-blue-50 border border-blue-200 text-blue-800 rounded-md p-4"
	default:
		return "bg-gray-50 border border-gray-200 text-gray-800 rounded-md p-4"
	}
}

// Компонент иконки для уведомлений
templ getAlertIcon(alertType string) {
	switch alertType {
	case "success":
		<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
			<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
		</svg>
	case "error":
		<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
			<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
		</svg>
	case "warning":
		<svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
			<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
		</svg>
	case "info":
		<svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
			<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
		</svg>
	default:
		<svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
			<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
		</svg>
	}
}

// Компонент для отображения загрузки
templ LoadingSpinner(size string) {
	<div class={ "htmx-indicator", getSpinnerClasses(size) }>
		<svg class="animate-spin text-primary-600" fill="none" viewBox="0 0 24 24">
			<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
			<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
		</svg>
	</div>
}

// Функция для получения размера спиннера
func getSpinnerClasses(size string) string {
	switch size {
	case "sm":
		return "inline-block h-4 w-4"
	case "lg":
		return "inline-block h-8 w-8"
	default:
		return "inline-block h-6 w-6"
	}
}

// Компонент пустого состояния
templ EmptyState(title string, description string, actionText string, actionUrl string) {
	<div class="text-center py-12">
		<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
		</svg>
		<h3 class="mt-2 text-sm font-medium text-gray-900">{ title }</h3>
		<p class="mt-1 text-sm text-gray-500">{ description }</p>
		if actionText != "" && actionUrl != "" {
			<div class="mt-6">
				<a href={ templ.URL(actionUrl) } class="btn btn-primary">
					{ actionText }
				</a>
			</div>
		}
	</div>
}
