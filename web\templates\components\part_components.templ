package components

import "velomasters/internal/database/queries"
import "fmt"

// PartCard компонент для отображения карточки запчасти
templ PartCard(part queries.ListPartsRow) {
	<div class="card mb-4 part-card" data-part-id={ part.ID.String() }>
		<div class="card-body">
			<div class="flex justify-between items-start mb-3">
				<div class="flex-1">
					<div class="flex items-center justify-between mb-2">
						<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{ part.Name }</h3>
						@PartStockBadge(part.QuantityInStock, part.MinStockLevel)
					</div>
					
					if part.Description.Valid && part.Description.String != "" {
						<p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{ part.Description.String }</p>
					}
					
					<div class="grid grid-cols-2 gap-4 text-sm mb-3">
						if part.PartNumber.Valid && part.PartNumber.String != "" {
							<div class="flex items-center text-gray-700 dark:text-gray-300">
								<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"/>
								</svg>
								<span class="font-medium">Артикул:</span>
								<span class="ml-1">{ part.PartNumber.String }</span>
							</div>
						}
						
						if part.Brand.Valid && part.Brand.String != "" {
							<div class="flex items-center text-gray-700 dark:text-gray-300">
								<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
								</svg>
								<span class="font-medium">Бренд:</span>
								<span class="ml-1">{ part.Brand.String }</span>
							</div>
						}
						
						if part.CategoryName.Valid && part.CategoryName.String != "" {
							<div class="flex items-center text-gray-700 dark:text-gray-300">
								<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
								</svg>
								<span class="font-medium">Категория:</span>
								<span class="ml-1">{ part.CategoryName.String }</span>
							</div>
						}
						
						if part.SupplierName.Valid && part.SupplierName.String != "" {
							<div class="flex items-center text-gray-700 dark:text-gray-300">
								<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
								</svg>
								<span class="font-medium">Поставщик:</span>
								<span class="ml-1">{ part.SupplierName.String }</span>
							</div>
						}
					</div>
					
					<div class="flex items-center space-x-4 text-sm">
						<div class="flex items-center text-green-600 dark:text-green-400">
							<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
							</svg>
							<span class="font-semibold">Цена: { formatPrice(part.Price) }</span>
						</div>
						
						<div class="flex items-center text-blue-600 dark:text-blue-400">
							<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
							</svg>
							<span>Остаток: { formatQuantity(part.QuantityInStock) }</span>
						</div>
					</div>
				</div>
				
				<div class="flex items-center space-x-2 ml-4">
					@PartStatusBadge(isPartActive(part))
				</div>
			</div>
			
			<div class="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700">
				<div class="text-sm text-gray-500 dark:text-gray-400">
					Создано: { formatDate(part.CreatedAt) }
				</div>
				<div class="flex space-x-2">
					<button 
						class="btn btn-sm btn-outline"
						hx-get={ "/api/htmx/parts/" + part.ID.String() + "/form" }
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('part-modal').classList.remove('hidden')"
					>
						Редактировать
					</button>
					<button 
						class="btn btn-sm btn-outline-danger"
						hx-delete={ "/api/parts/" + part.ID.String() }
						hx-target="closest .part-card"
						hx-swap="outerHTML"
						hx-confirm="Вы уверены, что хотите удалить эту запчасть?"
					>
						Удалить
					</button>
				</div>
			</div>
		</div>
	</div>
}

// PartList компонент для отображения списка запчастей
templ PartList(parts []queries.ListPartsRow, isEmpty bool) {
	<div id="parts-list" class="space-y-4">
		if isEmpty {
			<div class="text-center py-8">
				<svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Запчасти не найдены</h3>
				<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Начните с добавления новой запчасти в каталог.</p>
				<div class="mt-6">
					<button 
						class="btn btn-primary"
						hx-get="/api/htmx/parts/new/form"
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('part-modal').classList.remove('hidden')"
					>
						Добавить запчасть
					</button>
				</div>
			</div>
		} else {
			for _, part := range parts {
				@PartCard(part)
			}
		}
	</div>
}

// PartStockBadge компонент для отображения статуса остатков
templ PartStockBadge(quantity, minLevel pgtype.Int4) {
	if quantity.Valid && minLevel.Valid {
		if quantity.Int32 <= 0 {
			<span class="badge badge-danger">Нет в наличии</span>
		} else if quantity.Int32 <= minLevel.Int32 {
			<span class="badge badge-warning">Мало на складе</span>
		} else {
			<span class="badge badge-success">В наличии</span>
		}
	} else {
		<span class="badge badge-secondary">Неизвестно</span>
	}
}

// PartStatusBadge компонент для отображения статуса активности
templ PartStatusBadge(isActive bool) {
	if isActive {
		<span class="badge badge-success">Активна</span>
	} else {
		<span class="badge badge-secondary">Неактивна</span>
	}
}

// PartFilters компонент для фильтров запчастей
templ PartFilters(categories []queries.PartCategory, suppliers []queries.Supplier) {
	<div class="card mb-6">
		<div class="card-body">
			<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Фильтры</h3>
			
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
				<!-- Поиск -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Поиск</label>
					<input 
						type="text" 
						class="form-input w-full"
						placeholder="Название, артикул, бренд..."
						hx-get="/api/parts/search"
						hx-target="#parts-list"
						hx-trigger="keyup changed delay:300ms"
						hx-include="[name='category_filter'], [name='supplier_filter'], [name='stock_filter']"
						name="q"
					/>
				</div>
				
				<!-- Категория -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Категория</label>
					<select 
						class="form-select w-full"
						name="category_filter"
						hx-get="/api/parts"
						hx-target="#parts-list"
						hx-trigger="change"
						hx-include="[name='q'], [name='supplier_filter'], [name='stock_filter']"
					>
						<option value="">Все категории</option>
						for _, category := range categories {
							<option value={ category.ID.String() }>{ category.Name }</option>
						}
					</select>
				</div>
				
				<!-- Поставщик -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Поставщик</label>
					<select 
						class="form-select w-full"
						name="supplier_filter"
						hx-get="/api/parts"
						hx-target="#parts-list"
						hx-trigger="change"
						hx-include="[name='q'], [name='category_filter'], [name='stock_filter']"
					>
						<option value="">Все поставщики</option>
						for _, supplier := range suppliers {
							<option value={ supplier.ID.String() }>{ supplier.Name }</option>
						}
					</select>
				</div>
			</div>
			
			<div class="mt-4 flex items-center space-x-4">
				<!-- Фильтр по остаткам -->
				<div class="flex items-center space-x-2">
					<input 
						type="checkbox" 
						id="low-stock-filter"
						name="stock_filter"
						value="low"
						class="form-checkbox"
						hx-get="/api/parts"
						hx-target="#parts-list"
						hx-trigger="change"
						hx-include="[name='q'], [name='category_filter'], [name='supplier_filter']"
					/>
					<label for="low-stock-filter" class="text-sm text-gray-700 dark:text-gray-300">Только с низким остатком</label>
				</div>
				
				<!-- Сброс фильтров -->
				<button 
					class="btn btn-outline btn-sm"
					onclick="document.querySelectorAll('input[type=text], select, input[type=checkbox]').forEach(el => el.value = ''); htmx.trigger('#parts-list', 'refresh')"
				>
					Сбросить фильтры
				</button>
			</div>
		</div>
	</div>
}

// PartStats компонент для отображения статистики запчастей
templ PartStats(totalParts, lowStockParts, outOfStockParts int) {
	<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
		<div class="stat-card">
			<div class="stat-icon bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
				</svg>
			</div>
			<div class="stat-content">
				<div class="stat-title">Всего запчастей</div>
				<div class="stat-value">{ fmt.Sprintf("%d", totalParts) }</div>
			</div>
		</div>
		
		<div class="stat-card">
			<div class="stat-icon bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400">
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
				</svg>
			</div>
			<div class="stat-content">
				<div class="stat-title">Мало на складе</div>
				<div class="stat-value">{ fmt.Sprintf("%d", lowStockParts) }</div>
			</div>
		</div>
		
		<div class="stat-card">
			<div class="stat-icon bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400">
				<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
				</svg>
			</div>
			<div class="stat-content">
				<div class="stat-title">Нет в наличии</div>
				<div class="stat-value">{ fmt.Sprintf("%d", outOfStockParts) }</div>
			</div>
		</div>
	</div>
}

// PartForm компонент для формы создания/редактирования запчасти
templ PartForm(part *queries.GetPartRow, categories []queries.PartCategory, suppliers []queries.Supplier, isEdit bool) {
	<form
		if isEdit {
			hx-put={ "/api/parts/" + part.ID.String() }
		} else {
			hx-post="/api/parts"
		}
		hx-target="#parts-list"
		hx-swap="afterbegin"
		hx-on::after-request="if(event.detail.successful) document.getElementById('part-modal').classList.add('hidden')"
		class="space-y-6"
	>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<!-- Название -->
			<div class="md:col-span-2">
				<label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Название запчасти *
				</label>
				if isEdit {
					<input
						type="text"
						id="name"
						name="name"
						class="form-input w-full"
						value={ part.Name }
						required
						placeholder="Введите название запчасти"
					/>
				} else {
					<input
						type="text"
						id="name"
						name="name"
						class="form-input w-full"
						required
						placeholder="Введите название запчасти"
					/>
				}
			</div>

			<!-- Описание -->
			<div class="md:col-span-2">
				<label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Описание
				</label>
				<textarea
					id="description"
					name="description"
					rows="3"
					class="form-textarea w-full"
					placeholder="Описание запчасти"
				>if isEdit && part.Description.Valid { part.Description.String }</textarea>
			</div>

			<!-- Артикул -->
			<div>
				<label for="part_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Артикул
				</label>
				if isEdit && part.PartNumber.Valid {
					<input
						type="text"
						id="part_number"
						name="part_number"
						class="form-input w-full"
						value={ part.PartNumber.String }
						placeholder="Артикул запчасти"
					/>
				} else {
					<input
						type="text"
						id="part_number"
						name="part_number"
						class="form-input w-full"
						placeholder="Артикул запчасти"
					/>
				}
			</div>

			<!-- Бренд -->
			<div>
				<label for="brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Бренд
				</label>
				if isEdit && part.Brand.Valid {
					<input
						type="text"
						id="brand"
						name="brand"
						class="form-input w-full"
						value={ part.Brand.String }
						placeholder="Бренд запчасти"
					/>
				} else {
					<input
						type="text"
						id="brand"
						name="brand"
						class="form-input w-full"
						placeholder="Бренд запчасти"
					/>
				}
			</div>

			<!-- Категория -->
			<div>
				<label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Категория
				</label>
				<select id="category_id" name="category_id" class="form-select w-full">
					<option value="">Выберите категорию</option>
					for _, category := range categories {
						<option
							value={ category.ID.String() }
							if isEdit && part.CategoryID.Valid && part.CategoryID.Bytes == category.ID.Bytes {
								selected
							}
						>
							{ category.Name }
						</option>
					}
				</select>
			</div>

			<!-- Поставщик -->
			<div>
				<label for="supplier_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Поставщик
				</label>
				<select id="supplier_id" name="supplier_id" class="form-select w-full">
					<option value="">Выберите поставщика</option>
					for _, supplier := range suppliers {
						<option
							value={ supplier.ID.String() }
							if isEdit && part.SupplierID.Valid && part.SupplierID.Bytes == supplier.ID.Bytes {
								selected
							}
						>
							{ supplier.Name }
						</option>
					}
				</select>
			</div>

			<!-- Цена -->
			<div>
				<label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Цена продажи *
				</label>
				if isEdit && part.Price.Valid {
					<input
						type="number"
						id="price"
						name="price"
						step="0.01"
						min="0"
						class="form-input w-full"
						value={ fmt.Sprintf("%.2f", part.Price.Numeric.Float64) }
						required
						placeholder="0.00"
					/>
				} else {
					<input
						type="number"
						id="price"
						name="price"
						step="0.01"
						min="0"
						class="form-input w-full"
						value="0.00"
						required
						placeholder="0.00"
					/>
				}
			</div>

			<!-- Себестоимость -->
			<div>
				<label for="cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Себестоимость
				</label>
				if isEdit && part.Cost.Valid {
					<input
						type="number"
						id="cost"
						name="cost"
						step="0.01"
						min="0"
						class="form-input w-full"
						value={ fmt.Sprintf("%.2f", part.Cost.Numeric.Float64) }
						placeholder="0.00"
					/>
				} else {
					<input
						type="number"
						id="cost"
						name="cost"
						step="0.01"
						min="0"
						class="form-input w-full"
						value="0.00"
						placeholder="0.00"
					/>
				}
			</div>

			<!-- Количество на складе -->
			<div>
				<label for="quantity_in_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Количество на складе *
				</label>
				if isEdit && part.QuantityInStock.Valid {
					<input
						type="number"
						id="quantity_in_stock"
						name="quantity_in_stock"
						min="0"
						class="form-input w-full"
						value={ fmt.Sprintf("%d", part.QuantityInStock.Int32) }
						required
						placeholder="0"
					/>
				} else {
					<input
						type="number"
						id="quantity_in_stock"
						name="quantity_in_stock"
						min="0"
						class="form-input w-full"
						value="0"
						required
						placeholder="0"
					/>
				}
			</div>

			<!-- Минимальный остаток -->
			<div>
				<label for="min_stock_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
					Минимальный остаток
				</label>
				<input
					type="number"
					id="min_stock_level"
					name="min_stock_level"
					min="0"
					class="form-input w-full"
					if isEdit && part.MinStockLevel.Valid {
						value={ fmt.Sprintf("%d", part.MinStockLevel.Int32) }
					} else {
						value="1"
					}
					placeholder="1"
				/>
			</div>

			<!-- Активность -->
			<div class="md:col-span-2">
				<div class="flex items-center">
					<input
						type="checkbox"
						id="is_active"
						name="is_active"
						class="form-checkbox"
						if (isEdit && part.IsActive.Valid && part.IsActive.Bool) || (!isEdit) {
							checked
						}
					/>
					<label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
						Запчасть активна
					</label>
				</div>
			</div>
		</div>

		<div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
			<button
				type="button"
				class="btn btn-outline"
				onclick="document.getElementById('part-modal').classList.add('hidden')"
			>
				Отмена
			</button>
			<button type="submit" class="btn btn-primary">
				if isEdit {
					Сохранить изменения
				} else {
					Создать запчасть
				}
			</button>
		</div>
	</form>
}
