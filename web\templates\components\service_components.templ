package components

import "velomasters/internal/database/queries"
import "fmt"

// ServiceCard компонент для отображения карточки услуги
templ ServiceCard(service queries.Service) {
	<div class="card mb-4 service-card" data-service-id={ service.ID.String() }>
		<div class="card-body">
			<div class="flex justify-between items-start mb-3">
				<div class="flex-1">
					<h3 class="text-lg font-semibold text-gray-900 mb-1">{ service.Name }</h3>
					<p class="text-sm text-gray-600 mb-2">{ service.Description.String }</p>
					<div class="flex items-center space-x-4 text-sm">
						<div class="flex items-center text-green-600">
							<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
							</svg>
							<span class="font-semibold">{ formatPrice(service.Price) }</span>
						</div>
						if service.DurationMinutes.Valid {
							<div class="flex items-center text-blue-600">
								<svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
								</svg>
								<span>{ formatDuration(service.DurationMinutes.Int32) }</span>
							</div>
						}
						<!-- Категория будет добавлена позже -->
					</div>
				</div>
				<div class="flex items-center space-x-2 ml-4">
					@ServiceStatusBadge(isServiceActive(service))
				</div>
			</div>
			
			<div class="flex justify-between items-center pt-3 border-t border-gray-200">
				<div class="text-sm text-gray-500">
					Создано: { formatDate(service.CreatedAt) }
				</div>
				<div class="flex space-x-2">
					<button 
						class="btn btn-sm btn-outline"
						hx-get={ "/api/services/" + service.ID.String() + "/edit" }
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('service-modal').classList.remove('hidden')"
					>
						Редактировать
					</button>
					<button
						class={ "btn", "btn-sm", templ.KV("btn-danger", isServiceActive(service)), templ.KV("btn-success", !isServiceActive(service)) }
						hx-patch={ "/api/services/" + service.ID.String() + "/toggle" }
						hx-target="closest .service-card"
						hx-swap="outerHTML"
					>
						if isServiceActive(service) {
							Деактивировать
						} else {
							Активировать
						}
					</button>
					<button 
						class="btn btn-sm btn-primary"
						hx-post="/api/tasks/from-service"
						hx-vals={ fmt.Sprintf(`{"service_id": "%s"}`, service.ID.String()) }
						hx-target="#tasks-container"
						hx-trigger="click"
					>
						Создать задачу
					</button>
				</div>
			</div>
		</div>
	</div>
}

// ServiceStatusBadge компонент для отображения статуса услуги
templ ServiceStatusBadge(isActive bool) {
	if isActive {
		<span class="badge badge-success">Активна</span>
	} else {
		<span class="badge badge-secondary">Неактивна</span>
	}
}

// ServiceList компонент для отображения списка услуг
templ ServiceList(services []queries.Service, showEmpty bool) {
	<div id="services-container">
		if len(services) > 0 {
			for _, service := range services {
				@ServiceCard(service)
			}
		} else if showEmpty {
			@EmptyState("Нет услуг", "Пока нет созданных услуг. Добавьте первую услугу для начала работы.", "Добавить услугу", "/web/services/new")
		}
	</div>
}

// ServiceForm компонент для формы создания/редактирования услуги
templ ServiceForm(service *queries.Service, categories []string, isEdit bool) {
	<form 
		if isEdit {
			hx-put={ "/api/services/" + service.ID.String() }
		} else {
			hx-post="/api/services"
		}
		hx-target="#services-container"
		hx-swap="afterbegin"
		hx-on::after-request="if(event.detail.successful) { document.getElementById('service-modal').classList.add('hidden'); VeloMasters.forms.reset(this); }"
		class="space-y-4"
	>
		@FormField("Название услуги", "text", "name", getServiceValue(service, "name"), "Введите название услуги", true, nil)
		
		@FormField("Описание", "textarea", "description", getServiceValue(service, "description"), "Описание услуги", false, nil)
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			@FormField("Цена", "number", "price", getServiceValue(service, "price"), "0.00", true, nil)
			@FormField("Длительность (мин)", "number", "duration", getServiceValue(service, "duration"), "60", false, nil)
		</div>
		
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-2">Категория</label>
			<select name="category" class="form-select">
				<option value="">Выберите категорию</option>
				for _, category := range categories {
					<option value={ category }>
						{ category }
					</option>
				}
			</select>
		</div>
		
		<div class="flex items-center">
			<input
				type="checkbox"
				name="is_active"
				value="true"
				class="form-checkbox"
				if service == nil || isServiceActive(*service) {
					checked
				}
			/>
			<label class="ml-2 text-sm text-gray-700">Активная услуга</label>
		</div>
		
		<div class="flex justify-end space-x-3 pt-4">
			<button 
				type="button" 
				class="btn btn-outline"
				onclick="document.getElementById('service-modal').classList.add('hidden')"
			>
				Отмена
			</button>
			if isEdit {
				@FormButton("Сохранить", "submit", "primary", false, false)
			} else {
				@FormButton("Добавить услугу", "submit", "primary", false, false)
			}
		</div>
	</form>
}

// ServiceFilters компонент для фильтров услуг
templ ServiceFilters(categories []string) {
	<div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4 mb-6">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
			<div>
				@SearchBox("Поиск услуг...", "", "/api/services/search")
			</div>
			<div>
				<select 
					name="category" 
					class="form-select"
					hx-get="/api/services"
					hx-target="#services-container"
					hx-trigger="change"
					hx-include="[name='status'], [name='search']"
				>
					<option value="">Все категории</option>
					for _, category := range categories {
						<option value={ category }>{ category }</option>
					}
				</select>
			</div>
			<div>
				<select 
					name="status" 
					class="form-select"
					hx-get="/api/services"
					hx-target="#services-container"
					hx-trigger="change"
					hx-include="[name='category'], [name='search']"
				>
					<option value="">Все статусы</option>
					<option value="active">Активные</option>
					<option value="inactive">Неактивные</option>
				</select>
			</div>
			<div>
				<button 
					class="btn btn-primary w-full"
					hx-get="/api/services/new"
					hx-target="#modal-content"
					hx-trigger="click"
					onclick="document.getElementById('service-modal').classList.remove('hidden')"
				>
					+ Новая услуга
				</button>
			</div>
		</div>
	</div>
}

// ServiceStats компонент для статистики услуг
templ ServiceStats() {
	<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
		<div 
			class="card"
			hx-get="/api/services/stats/total"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Всего услуг</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/services/stats/active"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Активные</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/services/stats/popular"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Популярные</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
		
		<div 
			class="card"
			hx-get="/api/services/stats/revenue"
			hx-trigger="load"
		>
			<div class="card-body">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">Доход (месяц)</p>
						<p class="text-2xl font-semibold text-gray-900">...</p>
					</div>
				</div>
			</div>
		</div>
	</div>
}

// Вспомогательные функции
func isServiceActive(service queries.Service) bool {
	return service.IsActive.Valid && service.IsActive.Bool
}



func getServiceValue(service *queries.Service, field string) string {
	if service == nil {
		return ""
	}
	
	switch field {
	case "name":
		return service.Name
	case "description":
		return service.Description.String
	case "price":
		return fmt.Sprintf("%.2f", service.Price)
	case "duration":
		if service.DurationMinutes.Valid {
			return fmt.Sprintf("%d", service.DurationMinutes.Int32)
		}
		return ""
	default:
		return ""
	}
}


