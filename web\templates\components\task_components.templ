package components

import "velomasters/internal/database/queries"
import "fmt"

// TaskCard компонент для отображения карточки задачи
templ TaskCard(task queries.Task) {
	<div class="card mb-4 task-card" data-task-id={ task.ID.String() }>
		<div class="card-body">
			<div class="flex justify-between items-start mb-3">
				<div class="flex-1">
					<h3 class="text-lg font-semibold text-gray-900 mb-1">{ task.Title }</h3>
					<p class="text-sm text-gray-600">{ task.Description.String }</p>
				</div>
				<div class="flex items-center space-x-2 ml-4">
					@TaskStatusBadge(getTaskStatus(task))
					@TaskPriorityBadge(getTaskPriority(task))
				</div>
			</div>
			
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
				<div>
					<span class="font-medium">ID клиента:</span>
					<span class="ml-1">{ task.ClientID.String() }</span>
				</div>
				<div>
					<span class="font-medium">Стоимость:</span>
					<span class="ml-1 font-semibold">{ formatPrice(task.EstimatedCost) }</span>
				</div>
			</div>
			
			<div class="flex justify-between items-center">
				<div class="text-xs text-gray-500">
					Создано: { formatDate(task.CreatedAt) }
					if task.EstimatedCompletion.Valid {
						<span class="ml-3">Планируемое завершение: { formatDate(task.EstimatedCompletion) }</span>
					}
				</div>
				<div class="flex space-x-2">
					<button 
						class="btn btn-sm btn-outline"
						hx-get={ "/api/tasks/" + task.ID.String() + "/edit" }
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('task-modal').classList.remove('hidden')"
					>
						Редактировать
					</button>
					<button 
						class="btn btn-sm btn-primary"
						hx-patch={ "/api/tasks/" + task.ID.String() + "/status" }
						hx-vals='{"status": "next"}'
						hx-target="closest .task-card"
						hx-swap="outerHTML"
					>
						{ getNextStatusText(getTaskStatus(task)) }
					</button>
				</div>
			</div>
		</div>
	</div>
}

// TaskStatusBadge компонент для отображения статуса задачи
templ TaskStatusBadge(status string) {
	<span class={ "badge", getStatusClass(status) }>
		{ getStatusText(status) }
	</span>
}

// TaskPriorityBadge компонент для отображения приоритета задачи
templ TaskPriorityBadge(priority string) {
	if priority != "" {
		<span class={ "badge", getPriorityClass(priority) }>
			{ getPriorityText(priority) }
		</span>
	}
}

// TaskList компонент для отображения списка задач
templ TaskList(tasks []queries.Task, showEmpty bool) {
	<div id="tasks-container">
		if len(tasks) > 0 {
			for _, task := range tasks {
				@TaskCard(task)
			}
		} else if showEmpty {
			@EmptyState("Нет задач", "Пока нет созданных задач. Создайте первую задачу для начала работы.", "Создать задачу", "/web/tasks/new")
		}
	</div>
}

// TaskForm компонент для формы создания/редактирования задачи
templ TaskForm(task *queries.Task, clients []queries.Client, isEdit bool) {
	<form 
		if isEdit {
			hx-put={ "/api/tasks/" + task.ID.String() }
		} else {
			hx-post="/api/tasks"
		}
		hx-target="#tasks-container"
		hx-swap="afterbegin"
		hx-on::after-request="if(event.detail.successful) { document.getElementById('task-modal').classList.add('hidden'); VeloMasters.forms.reset(this); }"
		class="space-y-4"
	>
		@FormField("Название задачи", "text", "title", getTaskValue(task, "title"), "Введите название задачи", true, nil)
		
		@FormField("Описание", "textarea", "description", getTaskValue(task, "description"), "Описание работ", false, nil)
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-2">
					Клиент <span class="text-red-500">*</span>
				</label>
				<select name="client_id" class="form-select" required>
					<option value="">Выберите клиента</option>
					for _, client := range clients {
						<option
							value={ client.ID.String() }
							if task != nil && task.ClientID.Valid && task.ClientID.String() == client.ID.String() {
								selected
							}
						>
							{ client.Name } - { client.Phone.String }
						</option>
					}
				</select>
			</div>
			
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-2">Приоритет</label>
				<select name="priority" class="form-select">
					<option value="">Обычный</option>
					<option value="low"
						if task != nil && getTaskPriority(*task) == "low" {
							selected
						}
					>Низкий</option>
					<option value="medium"
						if task != nil && getTaskPriority(*task) == "medium" {
							selected
						}
					>Средний</option>
					<option value="high"
						if task != nil && getTaskPriority(*task) == "high" {
							selected
						}
					>Высокий</option>
					<option value="urgent"
						if task != nil && getTaskPriority(*task) == "urgent" {
							selected
						}
					>Срочный</option>
				</select>
			</div>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			@FormField("Предварительная стоимость", "number", "estimated_cost", getTaskValue(task, "estimated_cost"), "0.00", false, nil)
			@FormField("Срок выполнения", "date", "due_date", getTaskValue(task, "due_date"), "", false, nil)
		</div>
		
		<div class="flex justify-end space-x-3 pt-4">
			<button 
				type="button" 
				class="btn btn-outline"
				onclick="document.getElementById('task-modal').classList.add('hidden')"
			>
				Отмена
			</button>
			if isEdit {
				@FormButton("Сохранить", "submit", "primary", false, false)
			} else {
				@FormButton("Создать задачу", "submit", "primary", false, false)
			}
		</div>
	</form>
}

// TaskFilters компонент для фильтров задач
templ TaskFilters() {
	<div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4 mb-6">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
			<div>
				@SearchBox("Поиск задач...", "", "/api/tasks/search")
			</div>
			<div>
				<select 
					name="status" 
					class="form-select"
					hx-get="/api/tasks"
					hx-target="#tasks-container"
					hx-trigger="change"
					hx-include="[name='priority'], [name='search']"
				>
					<option value="">Все статусы</option>
					<option value="pending">Ожидает</option>
					<option value="in_progress">В работе</option>
					<option value="completed">Завершено</option>
					<option value="cancelled">Отменено</option>
				</select>
			</div>
			<div>
				<select 
					name="priority" 
					class="form-select"
					hx-get="/api/tasks"
					hx-target="#tasks-container"
					hx-trigger="change"
					hx-include="[name='status'], [name='search']"
				>
					<option value="">Все приоритеты</option>
					<option value="low">Низкий</option>
					<option value="medium">Средний</option>
					<option value="high">Высокий</option>
					<option value="urgent">Срочный</option>
				</select>
			</div>
			<div>
				<button 
					class="btn btn-primary w-full"
					hx-get="/api/tasks/new"
					hx-target="#modal-content"
					hx-trigger="click"
					onclick="document.getElementById('task-modal').classList.remove('hidden')"
				>
					+ Новая задача
				</button>
			</div>
		</div>
	</div>
}





func getTaskStatus(task queries.Task) string {
	if !task.Status.Valid {
		return "pending"
	}
	return string(task.Status.TaskStatus)
}

func getTaskPriority(task queries.Task) string {
	if !task.Priority.Valid {
		return "medium"
	}
	return string(task.Priority.TaskPriority)
}

func getNextStatusText(status string) string {
	switch status {
	case "pending":
		return "Начать работу"
	case "in_progress":
		return "Завершить"
	case "completed":
		return "Архивировать"
	default:
		return "Обновить статус"
	}
}

func getTaskValue(task *queries.Task, field string) string {
	if task == nil {
		return ""
	}
	
	switch field {
	case "title":
		return task.Title
	case "description":
		return task.Description.String
	case "estimated_cost":
		return formatPrice(task.EstimatedCost)
	case "estimated_completion":
		if task.EstimatedCompletion.Valid {
			return task.EstimatedCompletion.Time.Format("2006-01-02")
		}
		return ""
	default:
		return ""
	}
}

func getStatusClass(status string) string {
	switch status {
	case "pending":
		return "status-pending"
	case "in_progress":
		return "status-in-progress"
	case "completed":
		return "status-completed"
	case "cancelled":
		return "status-cancelled"
	default:
		return "badge-secondary"
	}
}

func getStatusText(status string) string {
	switch status {
	case "pending":
		return "Ожидает"
	case "in_progress":
		return "В работе"
	case "completed":
		return "Завершено"
	case "cancelled":
		return "Отменено"
	default:
		return "Неизвестно"
	}
}



func getPriorityClass(priority string) string {
	switch priority {
	case "low":
		return "priority-low"
	case "medium":
		return "priority-medium"
	case "high":
		return "priority-high"
	case "urgent":
		return "priority-urgent"
	default:
		return "badge-secondary"
	}
}

func getPriorityText(priority string) string {
	switch priority {
	case "low":
		return "Низкий"
	case "medium":
		return "Средний"
	case "high":
		return "Высокий"
	case "urgent":
		return "Срочный"
	default:
		return ""
	}
}

func formatPriceRub(price float64) string {
	return fmt.Sprintf("%.2f ₽", price)
}
