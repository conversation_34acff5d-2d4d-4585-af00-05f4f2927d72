package layouts

import "velomasters/web/templates/components"

templ Base(title string) {
	<!DOCTYPE html>
	<html lang="ru" class="h-full" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" x-bind:class="{ 'dark': darkMode }">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title } - VeloMasters</title>

			<!-- TailwindCSS -->
			<link rel="stylesheet" href="/static/css/output.css"/>

			<!-- HTMX -->
			<script src="https://unpkg.com/htmx.org@1.9.10"></script>

			<!-- Alpine.js для дополнительной интерактивности -->
			<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

			<!-- Иконки Heroicons -->
			<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/style.css"/>

			<!-- Основной JavaScript -->
			<script src="/static/js/app.js"></script>

			<!-- Скрипт для темной темы -->
			<script>
				// Инициализация темы до загрузки страницы
				if (localStorage.getItem('darkMode') === 'true' ||
					(!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
					document.documentElement.classList.add('dark');
				}
			</script>
			
			<style>
				/* Кастомные стили */
				.htmx-indicator {
					opacity: 0;
					transition: opacity 200ms ease-in;
				}
				.htmx-request .htmx-indicator {
					opacity: 1;
				}
				.htmx-request.htmx-indicator {
					opacity: 1;
				}
			</style>
		</head>
		<body class="h-full bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
			<div class="min-h-full">
				@components.Header()

				<main class="py-10">
					<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
						{ children... }
					</div>
				</main>

				@components.Footer()
			</div>

			<!-- Глобальные уведомления -->
			<div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>
		</body>
	</html>
}
