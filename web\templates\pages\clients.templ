package pages

import "velomasters/web/templates/layouts"
import "velomasters/web/templates/components"

templ Clients() {
	@layouts.Base("Клиенты") {
		<div class="container mx-auto px-4 py-8">
			<div class="flex justify-between items-center mb-8">
				<div>
					<h1 class="text-3xl font-bold text-gray-900">Клиенты</h1>
					<p class="mt-2 text-gray-600">База данных клиентов велосипедной мастерской</p>
				</div>
			</div>

			<!-- Статистика -->
			@components.ClientStats()

			<!-- Фильтры и поиск -->
			@components.ClientFilters()

			<!-- Список клиентов -->
			<div id="clients-container" hx-get="/api/clients" hx-trigger="load" hx-target="this">
				@components.LoadingSpinner("lg")
			</div>
		</div>

		<!-- Модальные окна -->
		@components.ClientModal()
		@components.ConfirmModal()

		<!-- Быстрые действия -->
		@components.QuickActions()

		<!-- Контейнер для уведомлений -->
		@components.NotificationContainer()
	}
}
