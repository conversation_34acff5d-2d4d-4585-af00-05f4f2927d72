package pages

import "velomasters/web/templates/layouts"
import "velomasters/web/templates/components"

templ Home() {
	@layouts.Base("Главная") {
		<!-- Hero секция -->
		<div class="text-center mb-12">
			<h1 class="text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-6xl">
				Вело-мастерская
			</h1>
			<p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
				Комплексная система учета для велосипедной мастерской.
				Управляйте задачами, клиентами, услугами и запчастями в одном месте.
			</p>
		</div>
		
		<!-- Статистика -->
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-12">
			@components.StatCard("Активные задачи", "12", "text-blue-600", "bg-blue-100")
			@components.StatCard("Клиенты", "48", "text-green-600", "bg-green-100")
			@components.StatCard("Услуги", "25", "text-purple-600", "bg-purple-100")
			@components.StatCard("Запчасти", "156", "text-orange-600", "bg-orange-100")
		</div>
		
		<!-- Быстрые действия -->
		<div class="stats-card p-6 mb-8">
			<h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Быстрые действия</h2>
			<div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
				@components.QuickActionCard("Новая задача", "Создать новую задачу ремонта", "/web/tasks/new", "M12 4.5v15m7.5-7.5h-15")
				@components.QuickActionCard("Добавить клиента", "Зарегистрировать нового клиента", "/web/clients/new", "M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z")
				@components.QuickActionCard("Новая услуга", "Добавить услугу в каталог", "/web/services/new", "M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z")
				@components.QuickActionCard("Управление запчастями", "Добавить или найти запчасти", "/web/parts", "M21.75 6.75a4.5 4.5 0 0 1-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 1 1-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 0 1 6.336-4.486l-3.276 3.277a3.004 3.004 0 0 1 2.25 2.25l3.276-3.276c.256.565.365 1.19.28 1.855Z")
			</div>
		</div>
		
		<!-- Последние задачи -->
		<div class="card">
			<div class="card-header">
				<h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Последние задачи</h2>
			</div>
			<div id="recent-tasks" hx-get="/api/tasks?limit=5" hx-trigger="load" hx-target="this">
				<div class="p-6 text-center">
					<div class="htmx-indicator">
						<svg class="animate-spin h-8 w-8 text-gray-400 mx-auto" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						<p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Загрузка задач...</p>
					</div>
				</div>
			</div>
		</div>
	}
}
