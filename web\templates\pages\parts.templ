package pages

import "velomasters/web/templates/layouts"
import "velomasters/web/templates/components"

templ Parts() {
	@layouts.Base("Каталог запчастей") {
		<div class="container mx-auto px-4 py-8">
			<div class="flex justify-between items-center mb-8">
				<div>
					<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Каталог запчастей</h1>
					<p class="mt-2 text-gray-600 dark:text-gray-400">Управление запчастями и складскими остатками</p>
				</div>
				<div class="flex space-x-3">
					<button 
						class="btn btn-outline"
						hx-get="/api/parts/low-stock"
						hx-target="#parts-list"
						hx-trigger="click"
					>
						<svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
						</svg>
						Низкие остатки
					</button>
					<button 
						class="btn btn-primary"
						hx-get="/api/htmx/parts/new/form"
						hx-target="#modal-content"
						hx-trigger="click"
						onclick="document.getElementById('part-modal').classList.remove('hidden')"
					>
						<svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
						</svg>
						Добавить запчасть
					</button>
				</div>
			</div>

			<!-- Статистика -->
			<div id="parts-stats" hx-get="/api/htmx/parts/stats" hx-trigger="load" hx-target="this">
				@components.PartStats(0, 0, 0)
			</div>

			<!-- Фильтры и поиск -->
			<div id="parts-filters" hx-get="/api/htmx/parts/filters" hx-trigger="load" hx-target="this">
				@components.LoadingSpinner("sm")
			</div>

			<!-- Список запчастей -->
			<div class="card">
				<div class="card-header">
					<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Запчасти</h2>
				</div>
				<div class="card-body">
					<div id="parts-list" hx-get="/api/parts" hx-trigger="load" hx-target="this">
						@components.LoadingSpinner("lg")
					</div>
				</div>
			</div>
		</div>

		<!-- Модальные окна -->
		@components.PartModal()
		@components.ConfirmModal()

		<!-- Быстрые действия -->
		@components.QuickActions()

		<!-- Контейнер для уведомлений -->
		@components.NotificationContainer()
	}
}
