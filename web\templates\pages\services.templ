package pages

import "velomasters/web/templates/layouts"
import "velomasters/web/templates/components"

templ Services() {
	@layouts.Base("Услуги") {
		<div class="container mx-auto px-4 py-8">
			<div class="flex justify-between items-center mb-8">
				<div>
					<h1 class="text-3xl font-bold text-gray-900">Услуги</h1>
					<p class="mt-2 text-gray-600">Каталог услуг велосипедной мастерской</p>
				</div>
			</div>

			<!-- Статистика -->
			@components.ServiceStats()

			<!-- Фильтры и поиск -->
			@components.ServiceFilters([]string{"Ремонт", "Обслуживание", "Настройка", "Сборка"})

			<!-- Список услуг -->
			<div id="services-container" hx-get="/api/services" hx-trigger="load" hx-target="this">
				@components.LoadingSpinner("lg")
			</div>
		</div>

		<!-- Модальные окна -->
		@components.ServiceModal()
		@components.ConfirmModal()

		<!-- Быстрые действия -->
		@components.QuickActions()

		<!-- Контейнер для уведомлений -->
		@components.NotificationContainer()
	}
}
