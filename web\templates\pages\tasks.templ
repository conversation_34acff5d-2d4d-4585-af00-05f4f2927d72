package pages

import "velomasters/web/templates/layouts"
import "velomasters/web/templates/components"

templ Tasks() {
	@layouts.Base("Задачи") {
		<div class="container mx-auto px-4 py-8">
			<div class="flex justify-between items-center mb-8">
				<div>
					<h1 class="text-3xl font-bold text-gray-900">Задачи</h1>
					<p class="mt-2 text-gray-600">Управление задачами ремонта велосипедов</p>
				</div>
			</div>

			<!-- Фильтры и поиск -->
			@components.TaskFilters()

			<!-- Список задач -->
			<div id="tasks-container" hx-get="/api/tasks" hx-trigger="load" hx-target="this">
				@components.LoadingSpinner("lg")
			</div>
		</div>

		<!-- Модальные окна -->
		@components.TaskModal()
		@components.ConfirmModal()

		<!-- Быстрые действия -->
		@components.QuickActions()

		<!-- Контейнер для уведомлений -->
		@components.NotificationContainer()
	}
}
